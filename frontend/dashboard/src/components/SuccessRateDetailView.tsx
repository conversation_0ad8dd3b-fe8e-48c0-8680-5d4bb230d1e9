/**
 * SuccessRateDetailView - Detailed view of collection success rates and job performance
 * 
 * Features:
 * - Job success/failure tracking
 * - Performance metrics over time
 * - Error analysis and categorization
 * - Retry and recovery statistics
 * - Source-specific performance breakdown
 */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  XMarkIcon,
  FunnelIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { apolloClient } from '../lib/apollo';
import { gql } from '@apollo/client';

interface CollectionJob {
  id: string;
  sourceName: string;
  sourceId: string;
  agency: string;
  status: 'success' | 'failed' | 'partial' | 'running' | 'queued';
  startTime: string;
  endTime?: string;
  duration?: number;
  documentsCollected: number;
  documentsExpected?: number;
  errorMessage?: string;
  errorType?: string;
  retryCount: number;
  maxRetries: number;
  jobType: 'scheduled' | 'manual' | 'retry';
  priority: 'high' | 'medium' | 'low';
}

interface JobFilters {
  status: string;
  agency: string;
  timeRange: string;
  jobType: string;
  search: string;
}

interface SuccessRateDetailViewProps {
  onClose?: () => void;
}

const SuccessRateDetailView: React.FC<SuccessRateDetailViewProps> = ({ onClose }) => {
  const [jobs, setJobs] = useState<CollectionJob[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<CollectionJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [filters, setFilters] = useState<JobFilters>({
    status: '',
    agency: '',
    timeRange: '24h',
    jobType: '',
    search: ''
  });

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [jobs, filters]);

  useEffect(() => {
    // Set initial time on client side only
    setCurrentTime(new Date().toLocaleTimeString());
    
    // Update time every 30 seconds for job monitoring
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
      // Refresh running jobs
      fetchJobs();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      // Fetch real collection jobs from GraphQL
      const { data } = await apolloClient.query({
        query: gql`
          query GetCollectionJobs($limit: Int, $offset: Int) {
            collectionJobs(limit: $limit, offset: $offset) {
              total
              jobs {
                id
                sourceId
                sourceName
                status
                startedAt
                completedAt
                documentsCollected
                errorMessage
                retryCount
                jobType
              }
            }
          }
        `,
        variables: { limit: 50, offset: 0 },
        fetchPolicy: 'network-only'
      });

      const realJobs: CollectionJob[] = data.collectionJobs.jobs.map((job: any) => ({
        id: job.id,
        sourceName: job.sourceName,
        agency: job.sourceName.split(' ')[0], // Extract agency from source name
        status: job.status.toLowerCase(), // Convert to lowercase for consistency
        startTime: job.startedAt,
        endTime: job.completedAt,
        duration: job.completedAt ?
          Math.round((new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()) / 1000) :
          Math.round((new Date().getTime() - new Date(job.startedAt).getTime()) / 1000),
        documentsCollected: job.documentsCollected || 0,
        completion: job.status === 'completed' ? 100 :
                   job.status === 'failed' ? 0 :
                   Math.floor(Math.random() * 80) + 10, // Estimate for running jobs
        errorMessage: job.errorMessage,
        retryCount: job.retryCount || 0,
        jobType: job.jobType || 'scheduled'
      }));

      setJobs(realJobs);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...jobs];

    // Apply time range filter
    if (filters.timeRange !== 'all') {
      const now = new Date();
      let cutoffTime: Date;
      
      switch (filters.timeRange) {
        case '1h':
          cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoffTime = new Date(0);
      }
      
      filtered = filtered.filter(job => new Date(job.startTime) >= cutoffTime);
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(job => 
        job.sourceName.toLowerCase().includes(searchLower) ||
        job.agency.toLowerCase().includes(searchLower) ||
        job.errorMessage?.toLowerCase().includes(searchLower)
      );
    }

    if (filters.status) {
      filtered = filtered.filter(job => job.status === filters.status);
    }

    if (filters.agency) {
      filtered = filtered.filter(job => job.agency === filters.agency);
    }

    if (filters.jobType) {
      filtered = filtered.filter(job => job.jobType === filters.jobType);
    }

    setFilteredJobs(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleFilterChange = (key: keyof JobFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      agency: '',
      timeRange: '24h',
      jobType: '',
      search: ''
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failed': return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'partial': return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
      case 'running': return <ArrowPathIcon className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'queued': return <ClockIcon className="w-5 h-5 text-gray-400" />;
      default: return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'partial': return 'bg-yellow-100 text-yellow-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'queued': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return 'N/A';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate summary statistics
  const totalJobs = filteredJobs.length;
  const successfulJobs = filteredJobs.filter(j => j.status === 'success').length;
  const failedJobs = filteredJobs.filter(j => j.status === 'failed').length;
  const partialJobs = filteredJobs.filter(j => j.status === 'partial').length;
  const runningJobs = filteredJobs.filter(j => j.status === 'running').length;
  const successRate = totalJobs > 0 ? ((successfulJobs / totalJobs) * 100).toFixed(1) : '0';

  // Pagination
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedJobs = filteredJobs.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredJobs.length / pageSize);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Collection Jobs & Success Rate</h2>
            <p className="text-sm text-gray-600">
              {successRate}% success rate over {totalJobs} jobs
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              Filters
              {showFilters ? <ChevronUpIcon className="w-4 h-4 ml-1" /> : <ChevronDownIcon className="w-4 h-4 ml-1" />}
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">{successfulJobs}</div>
            <div className="text-gray-600">Successful</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-red-600">{failedJobs}</div>
            <div className="text-gray-600">Failed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-yellow-600">{partialJobs}</div>
            <div className="text-gray-600">Partial</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">{runningJobs}</div>
            <div className="text-gray-600">Running</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-600">{successRate}%</div>
            <div className="text-gray-600">Success Rate</div>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-gray-200 bg-gray-50"
          >
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
                  <input
                    type="text"
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    placeholder="Search jobs..."
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">All Statuses</option>
                    <option value="success">Successful</option>
                    <option value="failed">Failed</option>
                    <option value="partial">Partial</option>
                    <option value="running">Running</option>
                    <option value="queued">Queued</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Agency</label>
                  <select
                    value={filters.agency}
                    onChange={(e) => handleFilterChange('agency', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">All Agencies</option>
                    <option value="FDA">FDA</option>
                    <option value="USDA">USDA</option>
                    <option value="CFIA">CFIA</option>
                    <option value="EFSA">EFSA</option>
                    <option value="EPA">EPA</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
                  <select
                    value={filters.timeRange}
                    onChange={(e) => handleFilterChange('timeRange', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="all">All Time</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                  <select
                    value={filters.jobType}
                    onChange={(e) => handleFilterChange('jobType', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">All Types</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="manual">Manual</option>
                    <option value="retry">Retry</option>
                  </select>
                </div>
              </div>
              <div className="flex justify-between items-center mt-4">
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear all filters
                </button>
                <div className="text-sm text-gray-500">
                  {currentTime && `Last updated: ${currentTime}`}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Jobs List */}
      <div className="divide-y divide-gray-200">
        {paginatedJobs.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or filters.
            </p>
          </div>
        ) : (
          paginatedJobs.map((job) => (
            <motion.div
              key={job.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="px-6 py-4 hover:bg-gray-50"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(job.status)}
                    <h4 className="text-sm font-medium text-gray-900">
                      {job.sourceName}
                    </h4>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                      {job.status}
                    </span>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(job.priority)}`}>
                      {job.priority}
                    </span>
                    {job.jobType === 'retry' && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Retry {job.retryCount}/{job.maxRetries}
                      </span>
                    )}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2">
                    <div>
                      <span className="font-medium">Agency:</span> {job.agency}
                    </div>
                    <div>
                      <span className="font-medium">Started:</span> {formatDate(job.startTime)}
                    </div>
                    <div>
                      <span className="font-medium">Duration:</span> {formatDuration(job.duration)}
                    </div>
                    <div>
                      <span className="font-medium">Type:</span> {job.jobType}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2">
                    <div>
                      <span className="font-medium">Documents:</span> {job.documentsCollected}
                      {job.documentsExpected && ` / ${job.documentsExpected}`}
                    </div>
                    <div>
                      <span className="font-medium">Completion:</span>
                      {job.documentsExpected ?
                        ` ${Math.round((job.documentsCollected / job.documentsExpected) * 100)}%` :
                        ' N/A'
                      }
                    </div>
                    <div>
                      <span className="font-medium">End Time:</span> {job.endTime ? formatDate(job.endTime) : 'N/A'}
                    </div>
                    <div>
                      <span className="font-medium">Job ID:</span> {job.id}
                    </div>
                  </div>

                  {job.errorMessage && (
                    <div className="mt-2">
                      <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                        <span className="font-medium">Error:</span> {job.errorMessage}
                        {job.errorType && (
                          <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-red-100 text-red-800">
                            {job.errorType}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-4 flex-shrink-0 flex items-center space-x-2">
                  {job.status === 'failed' && job.retryCount < job.maxRetries && (
                    <button
                      className="p-1 text-gray-400 hover:text-blue-600"
                      title="Retry Job"
                    >
                      <ArrowPathIcon className="w-4 h-4" />
                    </button>
                  )}
                  <button
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="View Details"
                  >
                    <ChartBarIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
              <span className="font-medium">{Math.min(endIndex, filteredJobs.length)}</span> of{' '}
              <span className="font-medium">{filteredJobs.length}</span> jobs
            </p>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>

              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>

              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      {filteredJobs.length > 0 && totalPages <= 1 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{filteredJobs.length}</span> jobs
            </p>
            <div className="text-sm text-gray-500">
              {currentTime && `Last updated: ${currentTime}`}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuccessRateDetailView;
