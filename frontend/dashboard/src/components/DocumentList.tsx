/**
 * DocumentList - Filterable document view for drill-down functionality
 * 
 * Features:
 * - Search and filter documents
 * - Sort by date, relevance, source
 * - Intent category filtering
 * - Source filtering
 * - Date range filtering
 * - Export functionality
 */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DocumentTextIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  TagIcon,
  BuildingOfficeIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';

interface Document {
  id: string;
  title: string;
  summary: string;
  source: string;
  agency: string;
  region: string;
  intentCategory: string;
  intentConfidence: number;
  publishedDate: string;
  ingestedDate: string;
  url: string;
  tags: string[];
  docType: string;
  language: string;
}

interface DocumentFilters {
  search: string;
  intent: string;
  source: string;
  dateRange: string;
  region: string;
  agency: string;
  docType: string;
}

interface DocumentListProps {
  initialFilters?: Partial<DocumentFilters>;
  onClose?: () => void;
}

const DocumentList: React.FC<DocumentListProps> = ({ initialFilters = {}, onClose }) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [paginatedDocuments, setPaginatedDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'date' | 'relevance' | 'source'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [filters, setFilters] = useState<DocumentFilters>({
    search: '',
    intent: '',
    source: '',
    dateRange: '7d',
    region: 'all',
    agency: '',
    docType: '',
    ...initialFilters
  });

  useEffect(() => {
    fetchDocuments();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [documents, filters, sortBy, sortOrder]);

  useEffect(() => {
    applyPagination();
  }, [filteredDocuments, currentPage, pageSize]);

  useEffect(() => {
    // Set initial time on client side only
    setCurrentTime(new Date().toLocaleTimeString());

    // Update time every minute
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      // Fetch real documents using DocumentService
      const realDocuments = await DocumentService.getDocuments({
        source: filters.source,
        intentCategory: filters.intentCategory,
        region: filters.region,
        searchTerm: filters.searchTerm
      });

      // Convert to component format
      const documents: Document[] = realDocuments.map(doc => ({
        id: doc.id,
        title: doc.title,
        summary: doc.summary,
        source: doc.source,
        agency: doc.source.split(' ')[0], // Extract agency from source
        region: doc.region,
        intentCategory: doc.intentCategories[0] || 'General',
        intentConfidence: doc.confidence / 100, // Convert to decimal
        publishedDate: doc.date,
        ingestedDate: doc.date, // Use same date if ingested date not available
        url: doc.filePath,
        tags: doc.intentCategories,
        docType: doc.type,
        language: 'en' // Default to English
      }));

      setTotalDocuments(documents.length);

      // Apply pagination
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedDocuments = documents.slice(startIndex, endIndex);

      setDocuments(paginatedDocuments);
        {
          id: '2',
          title: 'USDA FSIS Updates HACCP Verification Procedures',
          summary: 'New verification procedures for HACCP systems in meat and poultry establishments, focusing on enhanced monitoring and documentation requirements.',
          source: 'USDA FSIS',
          agency: 'USDA',
          region: 'US',
          intentCategory: 'Food Safety',
          intentConfidence: 0.92,
          publishedDate: '2024-01-14T14:20:00Z',
          ingestedDate: '2024-01-14T14:45:00Z',
          url: 'https://fsis.usda.gov/haccp-verification-2024',
          tags: ['HACCP', 'verification', 'meat', 'poultry', 'monitoring'],
          docType: 'Notice',
          language: 'en'
        },
        {
          id: '3',
          title: 'CFIA Announces New Organic Certification Standards',
          summary: 'Canadian Food Inspection Agency introduces updated organic certification standards with enhanced traceability requirements.',
          source: 'CFIA Newsroom',
          agency: 'CFIA',
          region: 'CA',
          intentCategory: 'Organic/Natural',
          intentConfidence: 0.88,
          publishedDate: '2024-01-13T09:15:00Z',
          ingestedDate: '2024-01-13T09:30:00Z',
          url: 'https://cfia.gc.ca/organic-standards-2024',
          tags: ['organic', 'certification', 'traceability', 'standards'],
          docType: 'Regulation',
          language: 'en'
        },
        {
          id: '4',
          title: 'EFSA Scientific Opinion on Novel Food Ingredients',
          summary: 'European Food Safety Authority publishes scientific opinion on the safety assessment of novel food ingredients derived from insects.',
          source: 'EFSA News',
          agency: 'EFSA',
          region: 'EU',
          intentCategory: 'Food Safety',
          intentConfidence: 0.90,
          publishedDate: '2024-01-12T16:45:00Z',
          ingestedDate: '2024-01-12T17:00:00Z',
          url: 'https://efsa.europa.eu/novel-food-insects-2024',
          tags: ['novel food', 'insects', 'safety assessment', 'scientific opinion'],
          docType: 'Scientific Opinion',
          language: 'en'
        },
        {
          id: '5',
          title: 'EPA Updates Chemical Residue Limits in Food Products',
          summary: 'Environmental Protection Agency revises maximum residue limits for pesticides in various food categories.',
          source: 'EPA Food Safety',
          agency: 'EPA',
          region: 'US',
          intentCategory: 'Additives/Chemicals',
          intentConfidence: 0.94,
          publishedDate: '2024-01-11T11:30:00Z',
          ingestedDate: '2024-01-11T12:00:00Z',
          url: 'https://epa.gov/pesticide-residues-2024',
          tags: ['pesticides', 'residue limits', 'chemicals', 'food safety'],
          docType: 'Regulation',
          language: 'en'
        }
      ];

      // Generate additional mock documents for pagination testing
      const mockDocuments: Document[] = [];
      const agencies = ['FDA', 'USDA', 'CFIA', 'EFSA', 'EPA'];
      const sources = ['FDA Food Safety', 'USDA FSIS', 'CFIA Newsroom', 'EFSA News', 'EPA Food Safety'];
      const regions = ['US', 'CA', 'EU'];
      const intents = ['Food Safety', 'Labeling', 'Organic/Natural', 'Additives/Chemicals', 'Trade/Import', 'ESG/Sustainability'];
      const docTypes = ['Guidance', 'Notice', 'Regulation', 'Scientific Opinion', 'Alert', 'Advisory'];

      // Add base documents
      mockDocuments.push(...baseMockDocuments);

      // Generate additional documents for pagination
      for (let i = 6; i <= 35; i++) {
        const randomAgency = agencies[Math.floor(Math.random() * agencies.length)];
        const randomSource = sources[Math.floor(Math.random() * sources.length)];
        const randomRegion = regions[Math.floor(Math.random() * regions.length)];
        const randomIntent = intents[Math.floor(Math.random() * intents.length)];
        const randomDocType = docTypes[Math.floor(Math.random() * docTypes.length)];
        const randomDate = new Date(2024, 0, Math.floor(Math.random() * 15) + 1);

        mockDocuments.push({
          id: i.toString(),
          title: `${randomAgency} ${randomDocType} on ${randomIntent} Requirements - Document ${i}`,
          summary: `This ${randomDocType.toLowerCase()} addresses important ${randomIntent.toLowerCase()} requirements and compliance measures for food industry stakeholders.`,
          source: randomSource,
          agency: randomAgency,
          region: randomRegion,
          intentCategory: randomIntent,
          intentConfidence: 0.75 + Math.random() * 0.25,
          publishedDate: randomDate.toISOString(),
          ingestedDate: new Date(randomDate.getTime() + 30 * 60 * 1000).toISOString(),
          url: `https://${randomAgency.toLowerCase()}.gov/document-${i}`,
          tags: [randomIntent.toLowerCase().replace('/', '-'), 'compliance', 'requirements'],
          docType: randomDocType,
          language: 'en'
        });
      }

      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...documents];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(doc => 
        doc.title.toLowerCase().includes(searchLower) ||
        doc.summary.toLowerCase().includes(searchLower) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply intent filter
    if (filters.intent) {
      filtered = filtered.filter(doc => doc.intentCategory === filters.intent);
    }

    // Apply source filter
    if (filters.source) {
      filtered = filtered.filter(doc => doc.source === filters.source);
    }

    // Apply agency filter
    if (filters.agency) {
      filtered = filtered.filter(doc => doc.agency === filters.agency);
    }

    // Apply region filter
    if (filters.region && filters.region !== 'all') {
      filtered = filtered.filter(doc => doc.region.toLowerCase() === filters.region.toLowerCase());
    }

    // Apply doc type filter
    if (filters.docType) {
      filtered = filtered.filter(doc => doc.docType === filters.docType);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const daysBack = parseInt(filters.dateRange.replace('d', ''));
      const cutoffDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
      filtered = filtered.filter(doc => new Date(doc.publishedDate) >= cutoffDate);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.publishedDate).getTime() - new Date(b.publishedDate).getTime();
          break;
        case 'relevance':
          comparison = b.intentConfidence - a.intentConfidence;
          break;
        case 'source':
          comparison = a.source.localeCompare(b.source);
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    setFilteredDocuments(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const applyPagination = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginated = filteredDocuments.slice(startIndex, endIndex);
    setPaginatedDocuments(paginated);
    setTotalPages(Math.ceil(filteredDocuments.length / pageSize));
  };

  const handleFilterChange = (key: keyof DocumentFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of document list
    document.querySelector('.document-list-container')?.scrollIntoView({ behavior: 'smooth' });
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      intent: '',
      source: '',
      dateRange: '7d',
      region: 'all',
      agency: '',
      docType: ''
    });
    setCurrentPage(1);
  };

  const viewAllDocuments = () => {
    clearFilters();
    setFilters(prev => ({ ...prev, dateRange: 'all' }));
  };

  const viewAllForCategory = (category: string) => {
    clearFilters();
    setFilters(prev => ({
      ...prev,
      intent: category,
      dateRange: 'all'
    }));
  };

  const exportDocuments = () => {
    const csvContent = [
      ['Title', 'Source', 'Agency', 'Intent Category', 'Published Date', 'URL'].join(','),
      ...filteredDocuments.map(doc => [
        `"${doc.title}"`,
        doc.source,
        doc.agency,
        doc.intentCategory,
        doc.publishedDate,
        doc.url
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `foodregs-documents-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getIntentColor = (intent: string) => {
    const colors: Record<string, string> = {
      'Food Safety': 'bg-blue-100 text-blue-800',
      'Labeling': 'bg-green-100 text-green-800',
      'ESG/Sustainability': 'bg-yellow-100 text-yellow-800',
      'Trade/Import': 'bg-red-100 text-red-800',
      'Organic/Natural': 'bg-purple-100 text-purple-800',
      'Additives/Chemicals': 'bg-cyan-100 text-cyan-800',
      'Workplace Safety': 'bg-lime-100 text-lime-800',
      'Quality Standards': 'bg-orange-100 text-orange-800'
    };
    return colors[intent] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg document-list-container">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Documents</h2>
            <p className="text-sm text-gray-600">
              Showing {paginatedDocuments.length} of {filteredDocuments.length} documents
              {filteredDocuments.length !== documents.length && ` (${documents.length} total)`}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              Filters
              {showFilters ? <ChevronUpIcon className="w-4 h-4 ml-1" /> : <ChevronDownIcon className="w-4 h-4 ml-1" />}
            </button>
            <button
              onClick={exportDocuments}
              className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
            >
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Export
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center space-x-3 text-sm">
          <button
            onClick={viewAllDocuments}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            View All Documents
          </button>
          <span className="text-gray-300">|</span>
          <div className="flex items-center space-x-2">
            <span className="text-gray-600">View all for:</span>
            {['Food Safety', 'Labeling', 'Organic/Natural', 'Additives/Chemicals'].map((category) => (
              <button
                key={category}
                onClick={() => viewAllForCategory(category)}
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-gray-200 bg-gray-50"
          >
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      placeholder="Search documents..."
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Intent Category</label>
                  <select
                    value={filters.intent}
                    onChange={(e) => handleFilterChange('intent', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">All Intents</option>
                    <option value="Food Safety">Food Safety</option>
                    <option value="Labeling">Labeling</option>
                    <option value="ESG/Sustainability">ESG/Sustainability</option>
                    <option value="Trade/Import">Trade/Import</option>
                    <option value="Organic/Natural">Organic/Natural</option>
                    <option value="Additives/Chemicals">Additives/Chemicals</option>
                    <option value="Workplace Safety">Workplace Safety</option>
                    <option value="Quality Standards">Quality Standards</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Agency</label>
                  <select
                    value={filters.agency}
                    onChange={(e) => handleFilterChange('agency', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">All Agencies</option>
                    <option value="FDA">FDA</option>
                    <option value="USDA">USDA</option>
                    <option value="CFIA">CFIA</option>
                    <option value="EFSA">EFSA</option>
                    <option value="EPA">EPA</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                  <select
                    value={filters.dateRange}
                    onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="1d">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                    <option value="90d">Last 90 Days</option>
                    <option value="all">All Time</option>
                  </select>
                </div>
              </div>
              <div className="flex justify-between items-center mt-4">
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear all filters
                </button>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">Per page:</label>
                    <select
                      value={pageSize}
                      onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                      className="border border-gray-300 rounded-md px-2 py-1 text-sm"
                    >
                      <option value="5">5</option>
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">Sort by:</label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as 'date' | 'relevance' | 'source')}
                      className="border border-gray-300 rounded-md px-2 py-1 text-sm"
                    >
                      <option value="date">Date</option>
                      <option value="relevance">Relevance</option>
                      <option value="source">Source</option>
                    </select>
                  </div>
                  <button
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {sortOrder === 'asc' ? '↑ Ascending' : '↓ Descending'}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Document List */}
      <div className="divide-y divide-gray-200">
        {filteredDocuments.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or filters.
            </p>
          </div>
        ) : (
          paginatedDocuments.map((doc) => (
            <motion.div
              key={doc.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="px-6 py-4 hover:bg-gray-50 cursor-pointer"
              onClick={() => window.open(doc.url, '_blank')}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2">
                    {doc.title}
                  </h4>
                  <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                    {doc.summary}
                  </p>
                  <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center">
                      <BuildingOfficeIcon className="w-3 h-3 mr-1" />
                      {doc.source}
                    </div>
                    <div className="flex items-center">
                      <CalendarIcon className="w-3 h-3 mr-1" />
                      {formatDate(doc.publishedDate)}
                    </div>
                    <div className="flex items-center">
                      <TagIcon className="w-3 h-3 mr-1" />
                      {doc.docType}
                    </div>
                  </div>
                  <div className="mt-2 flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getIntentColor(doc.intentCategory)}`}>
                      {doc.intentCategory}
                    </span>
                    <span className="text-xs text-gray-500">
                      {Math.round(doc.intentConfidence * 100)}% confidence
                    </span>
                    {doc.tags.slice(0, 3).map((tag) => (
                      <span key={tag} className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                        {tag}
                      </span>
                    ))}
                    {doc.tags.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{doc.tags.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    doc.region === 'US' ? 'bg-blue-100 text-blue-800' :
                    doc.region === 'CA' ? 'bg-red-100 text-red-800' :
                    doc.region === 'EU' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {doc.region}
                  </span>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Pagination Controls */}
      {filteredDocuments.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> to{' '}
                <span className="font-medium">{Math.min(currentPage * pageSize, filteredDocuments.length)}</span> of{' '}
                <span className="font-medium">{filteredDocuments.length}</span> documents
              </p>
              <div className="text-sm text-gray-500">
                {currentTime && `Last updated: ${currentTime}`}
              </div>
            </div>

            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (currentPage <= 4) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      pageNum = totalPages - 6 + i;
                    } else {
                      pageNum = currentPage - 3 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-1 text-sm border rounded-md ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'border-gray-300 hover:bg-gray-100'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentList;
