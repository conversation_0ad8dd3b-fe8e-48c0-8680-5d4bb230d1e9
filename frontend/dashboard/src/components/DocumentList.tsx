/**
 * DocumentList - Production-ready document list component
 * Uses REAL data only - NO MOCK DATA
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  CalendarIcon,
  TagIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { DocumentService } from '../services/documentService';

interface Document {
  id: string;
  title: string;
  summary: string;
  source: string;
  agency: string;
  region: string;
  intentCategory: string;
  intentConfidence: number;
  publishedDate: string;
  ingestedDate: string;
  url: string;
  tags: string[];
  docType: string;
  language: string;
}

interface DocumentListProps {
  filters?: {
    source?: string;
    intentCategory?: string;
    region?: string;
    searchTerm?: string;
  };
  onDocumentSelect?: (document: Document) => void;
}

const DocumentList: React.FC<DocumentListProps> = ({ 
  filters = {}, 
  onDocumentSelect 
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [localFilters, setLocalFilters] = useState({
    source: '',
    intentCategory: '',
    region: '',
    docType: ''
  });

  const itemsPerPage = 10;

  useEffect(() => {
    fetchDocuments();
  }, [filters, currentPage, searchTerm, localFilters]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Combine external filters with local filters
      const combinedFilters = {
        ...filters,
        ...localFilters,
        searchTerm: searchTerm || filters.searchTerm
      };

      // Remove empty filters
      const cleanFilters = Object.fromEntries(
        Object.entries(combinedFilters).filter(([_, value]) => value && value.trim() !== '')
      );

      // Fetch real documents using DocumentService
      const realDocuments = await DocumentService.getDocuments(cleanFilters);
      
      // Convert to component format
      const documents: Document[] = realDocuments.map(doc => ({
        id: doc.id,
        title: doc.title,
        summary: doc.summary,
        source: doc.source,
        agency: doc.source.split(' ')[0], // Extract agency from source
        region: doc.region,
        intentCategory: doc.intentCategories[0] || 'General',
        intentConfidence: doc.confidence / 100, // Convert to decimal
        publishedDate: doc.date,
        ingestedDate: doc.date, // Use same date if ingested date not available
        url: doc.filePath,
        tags: doc.intentCategories,
        docType: doc.type,
        language: 'en' // Default to English
      }));
      
      setTotalDocuments(documents.length);
      
      // Apply pagination
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedDocuments = documents.slice(startIndex, endIndex);
      
      setDocuments(paginatedDocuments);
      
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('Failed to fetch documents. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchDocuments();
  };

  const handleFilterChange = (filterType: string, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalDocuments / itemsPerPage);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 bg-green-50';
    if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading documents...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Documents</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
            <button 
              onClick={fetchDocuments}
              className="mt-2 text-sm text-red-800 underline hover:text-red-900"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <form onSubmit={handleSearch} className="flex gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Search
          </button>
        </form>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <select
            value={localFilters.source}
            onChange={(e) => handleFilterChange('source', e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Sources</option>
            <option value="FDA Food Safety">FDA Food Safety</option>
            <option value="USDA FSIS">USDA FSIS</option>
            <option value="CFIA Newsroom">CFIA Newsroom</option>
            <option value="EFSA News">EFSA News</option>
          </select>

          <select
            value={localFilters.intentCategory}
            onChange={(e) => handleFilterChange('intentCategory', e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Categories</option>
            <option value="Food Safety">Food Safety</option>
            <option value="Labeling">Labeling</option>
            <option value="Organic/Natural">Organic/Natural</option>
            <option value="Additives/Chemicals">Additives/Chemicals</option>
          </select>

          <select
            value={localFilters.region}
            onChange={(e) => handleFilterChange('region', e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Regions</option>
            <option value="US">United States</option>
            <option value="CA">Canada</option>
            <option value="EU">European Union</option>
          </select>

          <select
            value={localFilters.docType}
            onChange={(e) => handleFilterChange('docType', e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Types</option>
            <option value="Guidance">Guidance</option>
            <option value="Notice">Notice</option>
            <option value="Regulation">Regulation</option>
            <option value="Scientific Opinion">Scientific Opinion</option>
          </select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-600">
          Showing {documents.length} of {totalDocuments} documents
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Page {currentPage} of {totalPages}</span>
        </div>
      </div>

      {/* Document List */}
      <div className="space-y-4">
        {documents.map((document, index) => (
          <motion.div
            key={document.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onDocumentSelect?.(document)}
          >
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                {document.title}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(document.intentConfidence)}`}>
                {Math.round(document.intentConfidence * 100)}% confidence
              </span>
            </div>
            
            <p className="text-gray-600 mb-4 line-clamp-2">
              {document.summary}
            </p>
            
            <div className="flex flex-wrap gap-4 text-sm text-gray-500 mb-3">
              <div className="flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-1" />
                {document.source}
              </div>
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                {formatDate(document.publishedDate)}
              </div>
              <div className="flex items-center">
                <GlobeAltIcon className="h-4 w-4 mr-1" />
                {document.region}
              </div>
              <div className="flex items-center">
                <TagIcon className="h-4 w-4 mr-1" />
                {document.docType}
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {document.tags.slice(0, 3).map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
              {document.tags.length > 3 && (
                <span className="px-2 py-1 bg-gray-50 text-gray-500 rounded-full text-xs">
                  +{document.tags.length - 3} more
                </span>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </button>

          <div className="flex space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum = i + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-2 rounded-lg text-sm ${
                    currentPage === pageNum
                      ? 'bg-blue-600 text-white'
                      : 'border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Empty State */}
      {documents.length === 0 && !loading && (
        <div className="text-center py-12">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search terms or filters.
          </p>
        </div>
      )}
    </div>
  );
};

export default DocumentList;
