/**
 * Count Validation Utilities
 * 
 * This module provides comprehensive validation functions to ensure
 * data integrity and count accuracy across the FoodRegs dashboard.
 */

import { DocumentService, RegulatoryDocument } from '../services/documentService';
import { apolloClient } from '../lib/apollo';
import { gql } from '@apollo/client';

export interface CountValidationResult {
  isValid: boolean;
  expected: number;
  actual: number;
  description: string;
  errorMessage?: string;
}

export interface DashboardValidationReport {
  timestamp: string;
  overallValid: boolean;
  validationResults: CountValidationResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
  };
}

// GraphQL queries to match dashboard data sources
const GET_DASHBOARD_SUMMARY = gql`
  query GetDashboardSummary {
    dashboardSummary {
      totalDocuments
      totalSources
      activeSources
    }
  }
`;

const GET_REGULATIONS_COUNT = gql`
  query GetRegulationsCount {
    regulations {
      total
    }
  }
`;

/**
 * Validates dashboard summary metrics against actual data
 */
export class CountValidator {

  /**
   * Validate total document count using real GraphQL data
   */
  static async validateTotalDocuments(displayedCount: number): Promise<CountValidationResult> {
    try {
      // Use the same GraphQL query that the dashboard uses
      const { data } = await apolloClient.query({
        query: GET_DASHBOARD_SUMMARY,
        fetchPolicy: 'network-only' // Always get fresh data
      });

      const actualCount = data.dashboardSummary.totalDocuments;

      return {
        isValid: displayedCount === actualCount,
        expected: actualCount,
        actual: displayedCount,
        description: 'Total Documents Count (GraphQL)',
        errorMessage: displayedCount !== actualCount
          ? `Expected ${actualCount} from GraphQL but displayed ${displayedCount}`
          : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        expected: 0,
        actual: displayedCount,
        description: 'Total Documents Count (GraphQL)',
        errorMessage: `Error fetching GraphQL data: ${error}`
      };
    }
  }

  /**
   * Validate documents by source count
   */
  static async validateSourceDocumentCount(sourceName: string, displayedCount: number): Promise<CountValidationResult> {
    try {
      const sourceDocuments = await DocumentService.getDocumentsBySource(sourceName);
      const actualCount = sourceDocuments.length;
      
      return {
        isValid: displayedCount === actualCount,
        expected: actualCount,
        actual: displayedCount,
        description: `${sourceName} Document Count`,
        errorMessage: displayedCount !== actualCount 
          ? `Expected ${actualCount} but displayed ${displayedCount}` 
          : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        expected: 0,
        actual: displayedCount,
        description: `${sourceName} Document Count`,
        errorMessage: `Error fetching source documents: ${error}`
      };
    }
  }

  /**
   * Validate filtered document count
   */
  static async validateFilteredDocumentCount(
    filters: any, 
    displayedCount: number
  ): Promise<CountValidationResult> {
    try {
      const filteredDocuments = await DocumentService.getDocuments(filters);
      const actualCount = filteredDocuments.length;
      
      return {
        isValid: displayedCount === actualCount,
        expected: actualCount,
        actual: displayedCount,
        description: `Filtered Documents Count (${JSON.stringify(filters)})`,
        errorMessage: displayedCount !== actualCount 
          ? `Expected ${actualCount} but displayed ${displayedCount}` 
          : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        expected: 0,
        actual: displayedCount,
        description: `Filtered Documents Count`,
        errorMessage: `Error fetching filtered documents: ${error}`
      };
    }
  }

  /**
   * Validate intent category distribution counts
   */
  static async validateIntentDistribution(
    displayedDistribution: Array<{intent: string, count: number, percentage: number}>
  ): Promise<CountValidationResult[]> {
    try {
      const allDocuments = await DocumentService.getDocuments();
      const actualDistribution = this.calculateIntentDistribution(allDocuments);
      
      const results: CountValidationResult[] = [];
      
      for (const displayed of displayedDistribution) {
        const actual = actualDistribution.find(a => a.intent === displayed.intent);
        const actualCount = actual ? actual.count : 0;
        
        results.push({
          isValid: displayed.count === actualCount,
          expected: actualCount,
          actual: displayed.count,
          description: `Intent Distribution - ${displayed.intent}`,
          errorMessage: displayed.count !== actualCount 
            ? `Expected ${actualCount} but displayed ${displayed.count}` 
            : undefined
        });
      }
      
      return results;
    } catch (error) {
      return [{
        isValid: false,
        expected: 0,
        actual: 0,
        description: 'Intent Distribution Validation',
        errorMessage: `Error calculating intent distribution: ${error}`
      }];
    }
  }

  /**
   * Validate source performance metrics
   */
  static async validateSourcePerformance(
    displayedSources: Array<{source: string, documents: number, successRate: number}>
  ): Promise<CountValidationResult[]> {
    try {
      const results: CountValidationResult[] = [];
      
      for (const displayed of displayedSources) {
        const sourceDocuments = await DocumentService.getDocumentsBySource(displayed.source);
        const actualCount = sourceDocuments.length;
        
        results.push({
          isValid: displayed.documents === actualCount,
          expected: actualCount,
          actual: displayed.documents,
          description: `Source Performance - ${displayed.source} Document Count`,
          errorMessage: displayed.documents !== actualCount 
            ? `Expected ${actualCount} but displayed ${displayed.documents}` 
            : undefined
        });
      }
      
      return results;
    } catch (error) {
      return [{
        isValid: false,
        expected: 0,
        actual: 0,
        description: 'Source Performance Validation',
        errorMessage: `Error validating source performance: ${error}`
      }];
    }
  }

  /**
   * Validate unique sources count using real GraphQL data
   */
  static async validateUniqueSourcesCount(displayedCount: number): Promise<CountValidationResult> {
    try {
      // Use the same GraphQL query that the dashboard uses
      const { data } = await apolloClient.query({
        query: GET_DASHBOARD_SUMMARY,
        fetchPolicy: 'network-only' // Always get fresh data
      });

      const actualCount = data.dashboardSummary.totalSources;

      return {
        isValid: displayedCount === actualCount,
        expected: actualCount,
        actual: displayedCount,
        description: 'Total Sources Count (GraphQL)',
        errorMessage: displayedCount !== actualCount
          ? `Expected ${actualCount} from GraphQL but displayed ${displayedCount}`
          : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        expected: 0,
        actual: displayedCount,
        description: 'Total Sources Count (GraphQL)',
        errorMessage: `Error fetching GraphQL data: ${error}`
      };
    }
  }

  /**
   * Validate unique intent categories count
   */
  static async validateUniqueIntentCategoriesCount(displayedCount: number): Promise<CountValidationResult> {
    try {
      const uniqueCategories = DocumentService.getUniqueIntentCategories();
      const actualCount = uniqueCategories.length;
      
      return {
        isValid: displayedCount === actualCount,
        expected: actualCount,
        actual: displayedCount,
        description: 'Unique Intent Categories Count',
        errorMessage: displayedCount !== actualCount 
          ? `Expected ${actualCount} but displayed ${displayedCount}` 
          : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        expected: 0,
        actual: displayedCount,
        description: 'Unique Intent Categories Count',
        errorMessage: `Error fetching unique categories: ${error}`
      };
    }
  }

  /**
   * Calculate actual intent distribution from documents
   */
  private static calculateIntentDistribution(documents: RegulatoryDocument[]): Array<{intent: string, count: number, percentage: number}> {
    const intentCounts: {[key: string]: number} = {};
    const totalDocuments = documents.length;
    
    // Count occurrences of each intent category
    documents.forEach(doc => {
      doc.intentCategories.forEach(intent => {
        intentCounts[intent] = (intentCounts[intent] || 0) + 1;
      });
    });
    
    // Convert to distribution format
    return Object.entries(intentCounts).map(([intent, count]) => ({
      intent,
      count,
      percentage: totalDocuments > 0 ? (count / totalDocuments) * 100 : 0
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Run comprehensive validation of all dashboard counts with detailed logging
   */
  static async runComprehensiveValidation(
    dashboardData: any,
    logger?: (level: 'info' | 'success' | 'warning' | 'error', message: string, details?: any) => void
  ): Promise<DashboardValidationReport> {
    const results: CountValidationResult[] = [];
    const log = logger || (() => {}); // No-op if no logger provided

    log('info', 'Starting comprehensive dashboard validation');
    log('info', 'Dashboard data structure', {
      summaryKeys: Object.keys(dashboardData.summary || {}),
      intentDistributionCount: dashboardData.intentDistribution?.length || 0,
      sourcePerformanceCount: dashboardData.sourcePerformance?.length || 0
    });

    try {
      // Validate total documents
      log('info', 'Validating total documents count', {
        displayedCount: dashboardData.summary.totalDocuments
      });
      const totalDocsResult = await this.validateTotalDocuments(dashboardData.summary.totalDocuments);
      results.push(totalDocsResult);
      log(totalDocsResult.isValid ? 'success' : 'error',
          `Total documents validation: ${totalDocsResult.isValid ? 'PASSED' : 'FAILED'}`,
          totalDocsResult);

      // Validate intent distribution
      log('info', 'Validating intent distribution', {
        intentCount: dashboardData.intentDistribution?.length || 0
      });
      if (dashboardData.intentDistribution && dashboardData.intentDistribution.length > 0) {
        const intentResults = await this.validateIntentDistribution(dashboardData.intentDistribution);
        results.push(...intentResults);
        const intentPassed = intentResults.filter(r => r.isValid).length;
        log(intentPassed === intentResults.length ? 'success' : 'warning',
            `Intent distribution validation: ${intentPassed}/${intentResults.length} passed`);
      } else {
        log('warning', 'Intent distribution data is empty - skipping validation');
        results.push({
          isValid: false,
          expected: 1,
          actual: 0,
          description: 'Intent Distribution Data Availability',
          errorMessage: 'No intent distribution data found in dashboard'
        });
      }

      // Validate source performance
      log('info', 'Validating source performance data', {
        sourceCount: dashboardData.sourcePerformance?.length || 0
      });
      if (dashboardData.sourcePerformance && dashboardData.sourcePerformance.length > 0) {
        const sourceResults = await this.validateSourcePerformance(dashboardData.sourcePerformance);
        results.push(...sourceResults);
        const sourcePassed = sourceResults.filter(r => r.isValid).length;
        log(sourcePassed === sourceResults.length ? 'success' : 'warning',
            `Source performance validation: ${sourcePassed}/${sourceResults.length} passed`);
      } else {
        log('warning', 'Source performance data is empty - skipping validation');
        results.push({
          isValid: false,
          expected: 1,
          actual: 0,
          description: 'Source Performance Data Availability',
          errorMessage: 'No source performance data found in dashboard'
        });
      }

      // Validate unique sources count
      log('info', 'Validating unique sources count', {
        displayedCount: dashboardData.summary.totalSources
      });
      const uniqueSourcesResult = await this.validateUniqueSourcesCount(dashboardData.summary.totalSources);
      results.push(uniqueSourcesResult);
      log(uniqueSourcesResult.isValid ? 'success' : 'error',
          `Unique sources validation: ${uniqueSourcesResult.isValid ? 'PASSED' : 'FAILED'}`,
          uniqueSourcesResult);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log('error', 'Critical validation error occurred', { error: errorMessage });
      results.push({
        isValid: false,
        expected: 0,
        actual: 0,
        description: 'Comprehensive Validation',
        errorMessage: `Error during validation: ${errorMessage}`
      });
    }

    const passedTests = results.filter(r => r.isValid).length;
    const failedTests = results.filter(r => !r.isValid).length;

    log('info', 'Validation summary calculated', {
      totalTests: results.length,
      passedTests,
      failedTests,
      overallValid: failedTests === 0
    });

    const report = {
      timestamp: new Date().toISOString(),
      overallValid: failedTests === 0,
      validationResults: results,
      summary: {
        totalTests: results.length,
        passedTests,
        failedTests
      }
    };

    log('success', 'Comprehensive validation completed', { report });
    return report;
  }
}
