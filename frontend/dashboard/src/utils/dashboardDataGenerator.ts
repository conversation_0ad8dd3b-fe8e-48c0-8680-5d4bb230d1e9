/**
 * Dashboard Data Generator
 * 
 * Generates accurate dashboard data based on actual DocumentService data
 * to ensure data integrity and count accuracy.
 */

import { DocumentService } from '../services/documentService';
import { apolloClient } from '../lib/apollo';
import { gql } from '@apollo/client';

// GraphQL query for dashboard data
const GET_DASHBOARD_DATA = gql`
  query GetDashboardData {
    dashboardData {
      summary {
        totalSources
        activeSources
        totalDocuments
        documents24h
        documents7d
        healthySources
        failingSources
        overallSuccessRate
        totalActions
        pendingActions
      }
      sourceMetrics {
        sourceId
        name
        agency
        totalDocuments
        successRate7d
        lastCollectionAt
        lastSuccessAt
      }
      intentMetrics {
        intent
        totalDocuments
        documents24h
        documents7d
        avgConfidence
      }
      trendingTopics {
        topic
        mentions
        trend
        changePercent
      }
    }
  }
`;

export interface DashboardData {
  summary: {
    totalDocuments: number;
    documentsToday: number;
    activeSources: number;
    totalSources: number;
    healthySources: number;
    successRate: number;
    recentJobs: number;
    pendingActions: number;
    totalActions: number;
  };
  intentDistribution: Array<{
    intent: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  trendingTopics: Array<{
    topic: string;
    mentions: number;
    trend: string;
    change: number;
  }>;
  sourcePerformance: Array<{
    source: string;
    agency?: string;
    status: string;
    documents: number;
    successRate: number;
    lastCollection: string;
    primaryIntent: string;
  }>;
}

/**
 * Generate accurate dashboard data from DocumentService
 */
export async function generateAccurateDashboardData(): Promise<DashboardData> {
  try {
    // Try to get real data from GraphQL API first
    const { data } = await apolloClient.query({
      query: GET_DASHBOARD_DATA,
      errorPolicy: 'all'
    });

    if (data?.dashboardData) {
      const dashboardData = data.dashboardData;

      // Transform GraphQL data to our format
      return {
        summary: {
          totalDocuments: dashboardData.summary.totalDocuments,
          documentsToday: dashboardData.summary.documents24h,
          activeSources: dashboardData.summary.activeSources,
          totalSources: dashboardData.summary.totalSources,
          healthySources: dashboardData.summary.healthySources,
          successRate: dashboardData.summary.overallSuccessRate,
          recentJobs: Math.floor(dashboardData.summary.totalDocuments * 0.1),
          pendingActions: dashboardData.summary.pendingActions,
          totalActions: dashboardData.summary.totalActions
        },
        intentDistribution: dashboardData.intentMetrics?.map((intent: any, index: number) => ({
          intent: intent.intent,
          count: intent.totalDocuments,
          percentage: dashboardData.summary.totalDocuments > 0
            ? (intent.totalDocuments / dashboardData.summary.totalDocuments) * 100
            : 0,
          color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'][index % 8]
        })) || [],
        trendingTopics: dashboardData.trendingTopics?.map((topic: any) => ({
          topic: topic.topic,
          mentions: topic.mentions,
          trend: topic.trend,
          change: topic.changePercent
        })) || [],
        sourcePerformance: dashboardData.sourceMetrics?.map((source: any) => ({
          source: source.name,
          agency: source.agency,
          status: source.successRate7d > 90 ? 'healthy' : source.successRate7d > 70 ? 'warning' : 'error',
          documents: source.totalDocuments,
          successRate: source.successRate7d,
          lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : 'Never',
          primaryIntent: 'Food Safety' // Default for now
        })) || []
      };
    }

    // GraphQL data not available - return error instead of fallback
    console.error('GraphQL dashboard data not available and no fallback allowed');
    throw new Error('Dashboard data unavailable. Please check your connection and ensure the backend is running.');
  } catch (error) {
    console.error('Error generating dashboard data:', error);
    throw new Error('Failed to generate dashboard data. Please check your connection and ensure the backend is running.');
  }
}

/**
 * Validate dashboard data against DocumentService
 */
export async function validateDashboardData(dashboardData: DashboardData): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];
  
  try {
    const allDocuments = await DocumentService.getDocuments();
    const actualTotalDocuments = allDocuments.length;
    
    // Validate total documents
    if (dashboardData.summary.totalDocuments !== actualTotalDocuments) {
      errors.push(`Total documents mismatch: expected ${actualTotalDocuments}, got ${dashboardData.summary.totalDocuments}`);
    }
    
    // Validate source count
    const actualSources = await DocumentService.getUniqueSourcesAsync();
    if (dashboardData.summary.totalSources !== actualSources.length) {
      errors.push(`Total sources mismatch: expected ${actualSources.length}, got ${dashboardData.summary.totalSources}`);
    }
    
    // Validate source performance data
    for (const sourcePerf of dashboardData.sourcePerformance) {
      const sourceDocuments = await DocumentService.getDocumentsBySource(sourcePerf.source);
      if (sourcePerf.documents !== sourceDocuments.length) {
        errors.push(`Source ${sourcePerf.source} document count mismatch: expected ${sourceDocuments.length}, got ${sourcePerf.documents}`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
    
  } catch (error) {
    errors.push(`Validation error: ${error}`);
    return {
      isValid: false,
      errors
    };
  }
}
