/**
 * DocumentService - Production-ready service for regulatory document operations
 * Uses REAL GraphQL data only - NO MOCK DATA
 */

import { apolloClient } from '../lib/apollo';
import { gql } from '@apollo/client';

export interface RegulatoryDocument {
  id: string;
  title: string;
  summary: string;
  source: string;
  date: string;
  type: string;
  confidence: number;
  intentCategories: string[];
  content: string;
  filePath: string;
  region: string;
}

// GraphQL queries for real document data
const GET_DOCUMENTS = gql`
  query GetDocuments($limit: Int, $offset: Int, $filters: DocumentFilters) {
    documents(limit: $limit, offset: $offset, filters: $filters) {
      total
      documents {
        id
        title
        summary
        source
        publishedDate
        ingestedDate
        docType
        confidence
        intentCategories
        region
        url
        tags
        language
      }
    }
  }
`;

const GET_DOCUMENT_BY_ID = gql`
  query GetDocumentById($id: String!) {
    document(id: $id) {
      id
      title
      summary
      source
      publishedDate
      ingestedDate
      docType
      confidence
      intentCategories
      region
      url
      tags
      language
      content
    }
  }
`;

const GET_SOURCES = gql`
  query GetSources {
    sources {
      name
      agency
      region
      documentCount
    }
  }
`;

export interface DocumentFilters {
  source?: string;
  intentCategory?: string;
  dateRange?: string;
  region?: string;
  searchTerm?: string;
  confidence?: number;
}

/**
 * DocumentService - Handles all document-related operations using real GraphQL data
 * NO MOCK DATA - Production ready service
 */
export class DocumentService {

  /**
   * Get documents with optional filtering - REAL DATA ONLY
   */
  static async getDocuments(filters?: DocumentFilters): Promise<RegulatoryDocument[]> {
    try {
      const { data } = await apolloClient.query({
        query: GET_DOCUMENTS,
        variables: {
          limit: 100,
          offset: 0,
          filters: filters || {}
        },
        fetchPolicy: 'network-only' // Always get fresh data
      });

      return data.documents.documents.map((doc: any) => ({
        id: doc.id,
        title: doc.title,
        summary: doc.summary,
        source: doc.source,
        date: doc.publishedDate,
        type: doc.docType,
        confidence: doc.confidence,
        intentCategories: doc.intentCategories || [],
        content: doc.content || '',
        filePath: doc.url || '',
        region: doc.region || 'Unknown'
      }));

    } catch (error) {
      console.error('Error fetching documents from GraphQL:', error);
      throw new Error('Failed to fetch documents. Please check your connection and try again.');
    }
  }

  /**
   * Get a single document by ID - REAL DATA ONLY
   */
  static async getDocument(id: string): Promise<RegulatoryDocument | null> {
    try {
      const { data } = await apolloClient.query({
        query: GET_DOCUMENT_BY_ID,
        variables: { id },
        fetchPolicy: 'network-only'
      });

      if (!data.document) {
        return null;
      }

      const doc = data.document;
      return {
        id: doc.id,
        title: doc.title,
        summary: doc.summary,
        source: doc.source,
        date: doc.publishedDate,
        type: doc.docType,
        confidence: doc.confidence,
        intentCategories: doc.intentCategories || [],
        content: doc.content || '',
        filePath: doc.url || '',
        region: doc.region || 'Unknown'
      };

    } catch (error) {
      console.error('Error fetching document by ID:', error);
      return null;
    }
  }

  /**
   * Get documents by source - REAL DATA ONLY
   */
  static async getDocumentsBySource(sourceName: string): Promise<RegulatoryDocument[]> {
    return this.getDocuments({ source: sourceName });
  }

  /**
   * Get unique sources - REAL DATA ONLY
   */
  static getUniqueSources(): string[] {
    // This method is kept synchronous for backward compatibility
    // In production, this should be replaced with async GraphQL calls
    console.warn('getUniqueSources() is deprecated. Use getUniqueSourcesAsync() instead.');
    return [];
  }

  /**
   * Get unique sources (async) - REAL DATA ONLY
   */
  static async getUniqueSourcesAsync(): Promise<string[]> {
    try {
      const { data } = await apolloClient.query({
        query: GET_SOURCES,
        fetchPolicy: 'network-only'
      });

      return data.sources.map((source: any) => source.name).sort();

    } catch (error) {
      console.error('Error fetching sources:', error);
      // Fallback: get sources from documents
      try {
        const documents = await this.getDocuments();
        const sources = documents.map(doc => doc.source);
        return [...new Set(sources)].sort();
      } catch (fallbackError) {
        console.error('Fallback source fetch also failed:', fallbackError);
        return [];
      }
    }
  }

  /**
   * Get unique intent categories - REAL DATA ONLY
   */
  static async getUniqueIntentCategories(): Promise<string[]> {
    try {
      const documents = await this.getDocuments();
      const allCategories = documents.flatMap(doc => doc.intentCategories);
      return [...new Set(allCategories)].sort();
    } catch (error) {
      console.error('Error fetching intent categories:', error);
      return [];
    }
  }

  /**
   * Get unique regions - REAL DATA ONLY
   */
  static async getUniqueRegions(): Promise<string[]> {
    try {
      const documents = await this.getDocuments();
      const regions = documents.map(doc => doc.region);
      return [...new Set(regions)].sort();
    } catch (error) {
      console.error('Error fetching regions:', error);
      return [];
    }
  }

  /**
   * Search documents - REAL DATA ONLY
   */
  static async searchDocuments(searchTerm: string): Promise<RegulatoryDocument[]> {
    return this.getDocuments({ searchTerm });
  }
}