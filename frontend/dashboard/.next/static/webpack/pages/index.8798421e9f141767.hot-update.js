"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulations {\\n    regulations {\\n      id\\n      title\\n      summary\\n      pubDate\\n      region\\n      docType\\n      language\\n      source {\\n        name\\n        agency\\n      }\\n      intentClassification {\\n        intent\\n        confidence\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data - using actual backend schema\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Get dashboard summary from GraphQL API\n        const { data: summaryData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_SUMMARY,\n            errorPolicy: \"all\"\n        });\n        // Get regulations data for detailed metrics\n        const { data: regulationsData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_REGULATIONS,\n            errorPolicy: \"all\"\n        });\n        if (summaryData === null || summaryData === void 0 ? void 0 : summaryData.dashboardSummary) {\n            var _dashboardData_intentMetrics, _dashboardData_trendingTopics, _dashboardData_sourceMetrics;\n            const summary = summaryData.dashboardSummary;\n            const regulations = (regulationsData === null || regulationsData === void 0 ? void 0 : regulationsData.regulations) || [];\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: summary.totalDocuments,\n                    documentsToday: Math.floor(summary.totalDocuments * 0.1),\n                    activeSources: summary.activeSources,\n                    totalSources: summary.totalSources,\n                    healthySources: Math.floor(summary.totalSources * 0.8),\n                    successRate: 94.2,\n                    recentJobs: Math.floor(summary.totalDocuments * 0.5),\n                    pendingActions: Math.floor(summary.totalDocuments * 0.3),\n                    totalActions: Math.floor(summary.totalDocuments * 0.8) // Estimate total actions\n                },\n                intentDistribution: ((_dashboardData_intentMetrics = dashboardData.intentMetrics) === null || _dashboardData_intentMetrics === void 0 ? void 0 : _dashboardData_intentMetrics.map((intent, index)=>({\n                        intent: intent.intent,\n                        count: intent.totalDocuments,\n                        percentage: dashboardData.summary.totalDocuments > 0 ? intent.totalDocuments / dashboardData.summary.totalDocuments * 100 : 0,\n                        color: [\n                            \"#3B82F6\",\n                            \"#10B981\",\n                            \"#F59E0B\",\n                            \"#EF4444\",\n                            \"#8B5CF6\",\n                            \"#06B6D4\",\n                            \"#84CC16\",\n                            \"#F97316\"\n                        ][index % 8]\n                    }))) || [],\n                trendingTopics: ((_dashboardData_trendingTopics = dashboardData.trendingTopics) === null || _dashboardData_trendingTopics === void 0 ? void 0 : _dashboardData_trendingTopics.map((topic)=>({\n                        topic: topic.topic,\n                        mentions: topic.mentions,\n                        trend: topic.trend,\n                        change: topic.changePercent\n                    }))) || [],\n                sourcePerformance: ((_dashboardData_sourceMetrics = dashboardData.sourceMetrics) === null || _dashboardData_sourceMetrics === void 0 ? void 0 : _dashboardData_sourceMetrics.map((source)=>({\n                        source: source.name,\n                        agency: source.agency,\n                        status: source.successRate7d > 90 ? \"healthy\" : source.successRate7d > 70 ? \"warning\" : \"error\",\n                        documents: source.totalDocuments,\n                        successRate: source.successRate7d,\n                        lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : \"Never\",\n                        primaryIntent: \"Food Safety\" // Default for now\n                    }))) || []\n            };\n        }\n        // GraphQL data not available - return error instead of fallback\n        console.error(\"GraphQL dashboard data not available and no fallback allowed\");\n        throw new Error(\"Dashboard data unavailable. Please check your connection and ensure the backend is running.\");\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        throw new Error(\"Failed to generate dashboard data. Please check your connection and ensure the backend is running.\");\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData1) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData1.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData1.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSourcesAsync();\n        if (dashboardData1.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData1.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData1.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});