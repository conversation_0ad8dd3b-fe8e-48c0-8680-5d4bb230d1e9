"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulations {\\n    regulations {\\n      id\\n      title\\n      summary\\n      pubDate\\n      region\\n      docType\\n      language\\n      source {\\n        name\\n        agency\\n      }\\n      intentClassification {\\n        intent\\n        confidence\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data - using actual backend schema\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Get dashboard summary from GraphQL API\n        const { data: summaryData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_SUMMARY,\n            errorPolicy: \"all\"\n        });\n        // Get regulations data for detailed metrics\n        const { data: regulationsData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_REGULATIONS,\n            errorPolicy: \"all\"\n        });\n        if (summaryData === null || summaryData === void 0 ? void 0 : summaryData.dashboardSummary) {\n            var _dashboardData_intentMetrics, _dashboardData_trendingTopics, _dashboardData_sourceMetrics;\n            const summary = summaryData.dashboardSummary;\n            const regulations = (regulationsData === null || regulationsData === void 0 ? void 0 : regulationsData.regulations) || [];\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: dashboardData.summary.totalDocuments,\n                    documentsToday: dashboardData.summary.documents24h,\n                    activeSources: dashboardData.summary.activeSources,\n                    totalSources: dashboardData.summary.totalSources,\n                    healthySources: dashboardData.summary.healthySources,\n                    successRate: dashboardData.summary.overallSuccessRate,\n                    recentJobs: Math.floor(dashboardData.summary.totalDocuments * 0.1),\n                    pendingActions: dashboardData.summary.pendingActions,\n                    totalActions: dashboardData.summary.totalActions\n                },\n                intentDistribution: ((_dashboardData_intentMetrics = dashboardData.intentMetrics) === null || _dashboardData_intentMetrics === void 0 ? void 0 : _dashboardData_intentMetrics.map((intent, index)=>({\n                        intent: intent.intent,\n                        count: intent.totalDocuments,\n                        percentage: dashboardData.summary.totalDocuments > 0 ? intent.totalDocuments / dashboardData.summary.totalDocuments * 100 : 0,\n                        color: [\n                            \"#3B82F6\",\n                            \"#10B981\",\n                            \"#F59E0B\",\n                            \"#EF4444\",\n                            \"#8B5CF6\",\n                            \"#06B6D4\",\n                            \"#84CC16\",\n                            \"#F97316\"\n                        ][index % 8]\n                    }))) || [],\n                trendingTopics: ((_dashboardData_trendingTopics = dashboardData.trendingTopics) === null || _dashboardData_trendingTopics === void 0 ? void 0 : _dashboardData_trendingTopics.map((topic)=>({\n                        topic: topic.topic,\n                        mentions: topic.mentions,\n                        trend: topic.trend,\n                        change: topic.changePercent\n                    }))) || [],\n                sourcePerformance: ((_dashboardData_sourceMetrics = dashboardData.sourceMetrics) === null || _dashboardData_sourceMetrics === void 0 ? void 0 : _dashboardData_sourceMetrics.map((source)=>({\n                        source: source.name,\n                        agency: source.agency,\n                        status: source.successRate7d > 90 ? \"healthy\" : source.successRate7d > 70 ? \"warning\" : \"error\",\n                        documents: source.totalDocuments,\n                        successRate: source.successRate7d,\n                        lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : \"Never\",\n                        primaryIntent: \"Food Safety\" // Default for now\n                    }))) || []\n            };\n        }\n        // GraphQL data not available - return error instead of fallback\n        console.error(\"GraphQL dashboard data not available and no fallback allowed\");\n        throw new Error(\"Dashboard data unavailable. Please check your connection and ensure the backend is running.\");\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        throw new Error(\"Failed to generate dashboard data. Please check your connection and ensure the backend is running.\");\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData1) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData1.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData1.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSourcesAsync();\n        if (dashboardData1.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData1.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData1.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});