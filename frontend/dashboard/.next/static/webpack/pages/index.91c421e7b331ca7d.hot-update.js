"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/services/documentService.ts":
/*!*****************************************!*\
  !*** ./src/services/documentService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentService: function() { return /* binding */ DocumentService; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * DocumentService - Production-ready service for regulatory document operations\n * Uses REAL GraphQL data only - NO MOCK DATA\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDocuments($limit: Int, $offset: Int, $filters: DocumentFilters) {\\n    documents(limit: $limit, offset: $offset, filters: $filters) {\\n      total\\n      documents {\\n        id\\n        title\\n        summary\\n        source\\n        publishedDate\\n        ingestedDate\\n        docType\\n        confidence\\n        intentCategories\\n        region\\n        url\\n        tags\\n        language\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDocumentById($id: String!) {\\n    document(id: $id) {\\n      id\\n      title\\n      summary\\n      source\\n      publishedDate\\n      ingestedDate\\n      docType\\n      confidence\\n      intentCategories\\n      region\\n      url\\n      tags\\n      language\\n      content\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetSources {\\n    sources {\\n      name\\n      agency\\n      region\\n      documentCount\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n// GraphQL queries for real document data\nconst GET_DOCUMENTS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.gql)(_templateObject());\nconst GET_DOCUMENT_BY_ID = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.gql)(_templateObject1());\nconst GET_SOURCES = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.gql)(_templateObject2());\n/**\n * DocumentService - Handles all document-related operations using real GraphQL data\n * NO MOCK DATA - Production ready service\n */ class DocumentService {\n    /**\n   * Get documents with optional filtering - REAL DATA ONLY\n   */ static async getDocuments(filters) {\n        try {\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_1__.apolloClient.query({\n                query: GET_DOCUMENTS,\n                variables: {\n                    limit: 100,\n                    offset: 0,\n                    filters: filters || {}\n                },\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            return data.documents.documents.map((doc)=>({\n                    id: doc.id,\n                    title: doc.title,\n                    summary: doc.summary,\n                    source: doc.source,\n                    date: doc.publishedDate,\n                    type: doc.docType,\n                    confidence: doc.confidence,\n                    intentCategories: doc.intentCategories || [],\n                    content: doc.content || \"\",\n                    filePath: doc.url || \"\",\n                    region: doc.region || \"Unknown\"\n                }));\n        } catch (error) {\n            console.error(\"Error fetching documents from GraphQL:\", error);\n            throw new Error(\"Failed to fetch documents. Please check your connection and try again.\");\n        }\n    }\n    /**\n   * Get a single document by ID - REAL DATA ONLY\n   */ static async getDocument(id) {\n        try {\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_1__.apolloClient.query({\n                query: GET_DOCUMENT_BY_ID,\n                variables: {\n                    id\n                },\n                fetchPolicy: \"network-only\"\n            });\n            if (!data.document) {\n                return null;\n            }\n            const doc = data.document;\n            return {\n                id: doc.id,\n                title: doc.title,\n                summary: doc.summary,\n                source: doc.source,\n                date: doc.publishedDate,\n                type: doc.docType,\n                confidence: doc.confidence,\n                intentCategories: doc.intentCategories || [],\n                content: doc.content || \"\",\n                filePath: doc.url || \"\",\n                region: doc.region || \"Unknown\"\n            };\n        } catch (error) {\n            console.error(\"Error fetching document by ID:\", error);\n            return null;\n        }\n    }\n    /**\n   * Get documents by source - REAL DATA ONLY\n   */ static async getDocumentsBySource(sourceName) {\n        return this.getDocuments({\n            source: sourceName\n        });\n    }\n    /**\n   * Get unique sources - REAL DATA ONLY\n   */ static getUniqueSources() {\n        // This method is kept synchronous for backward compatibility\n        // In production, this should be replaced with async GraphQL calls\n        console.warn(\"getUniqueSources() is deprecated. Use getUniqueSourcesAsync() instead.\");\n        return [];\n    }\n    /**\n   * Get unique sources (async) - REAL DATA ONLY\n   */ static async getUniqueSourcesAsync() {\n        try {\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_1__.apolloClient.query({\n                query: GET_SOURCES,\n                fetchPolicy: \"network-only\"\n            });\n            return data.sources.map((source)=>source.name).sort();\n        } catch (error) {\n            console.error(\"Error fetching sources:\", error);\n            // Fallback: get sources from documents\n            try {\n                const documents = await this.getDocuments();\n                const sources = documents.map((doc)=>doc.source);\n                return [\n                    ...new Set(sources)\n                ].sort();\n            } catch (fallbackError) {\n                console.error(\"Fallback source fetch also failed:\", fallbackError);\n                return [];\n            }\n        }\n    }\n    /**\n   * Get unique intent categories - REAL DATA ONLY\n   */ static async getUniqueIntentCategories() {\n        try {\n            const documents = await this.getDocuments();\n            const allCategories = documents.flatMap((doc)=>doc.intentCategories);\n            return [\n                ...new Set(allCategories)\n            ].sort();\n        } catch (error) {\n            console.error(\"Error fetching intent categories:\", error);\n            return [];\n        }\n    }\n    /**\n   * Get unique regions - REAL DATA ONLY\n   */ static async getUniqueRegions() {\n        try {\n            const documents = await this.getDocuments();\n            const regions = documents.map((doc)=>doc.region);\n            return [\n                ...new Set(regions)\n            ].sort();\n        } catch (error) {\n            console.error(\"Error fetching regions:\", error);\n            return [];\n        }\n    }\n    /**\n   * Search documents - REAL DATA ONLY\n   */ static async searchDocuments(searchTerm) {\n        return this.getDocuments({\n            searchTerm\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/documentService.ts\n"));

/***/ })

});