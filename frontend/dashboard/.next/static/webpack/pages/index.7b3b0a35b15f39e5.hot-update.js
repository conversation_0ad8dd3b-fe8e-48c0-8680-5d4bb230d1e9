"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/countValidation.ts":
/*!**************************************!*\
  !*** ./src/utils/countValidation.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountValidator: function() { return /* binding */ CountValidator; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Count Validation Utilities\n * \n * This module provides comprehensive validation functions to ensure\n * data integrity and count accuracy across the FoodRegs dashboard.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulationsCount {\\n    regulations {\\n      total\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL queries to match dashboard data sources\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS_COUNT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Validates dashboard summary metrics against actual data\n */ class CountValidator {\n    /**\n   * Validate total document count using real GraphQL data\n   */ static async validateTotalDocuments(displayedCount) {\n        try {\n            // Use the same GraphQL query that the dashboard uses\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n                query: GET_DASHBOARD_SUMMARY,\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            const actualCount = data.dashboardSummary.totalDocuments;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" from GraphQL but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: \"Error fetching GraphQL data: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate documents by source count\n   */ static async validateSourceDocumentCount(sourceName, displayedCount) {\n        try {\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourceName);\n            const actualCount = sourceDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: \"Error fetching source documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate filtered document count\n   */ static async validateFilteredDocumentCount(filters, displayedCount) {\n        try {\n            const filteredDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments(filters);\n            const actualCount = filteredDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Filtered Documents Count (\".concat(JSON.stringify(filters), \")\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Filtered Documents Count\",\n                errorMessage: \"Error fetching filtered documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate intent category distribution counts\n   */ static async validateIntentDistribution(displayedDistribution) {\n        try {\n            const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n            const actualDistribution = this.calculateIntentDistribution(allDocuments);\n            const results = [];\n            for (const displayed of displayedDistribution){\n                const actual = actualDistribution.find((a)=>a.intent === displayed.intent);\n                const actualCount = actual ? actual.count : 0;\n                results.push({\n                    isValid: displayed.count === actualCount,\n                    expected: actualCount,\n                    actual: displayed.count,\n                    description: \"Intent Distribution - \".concat(displayed.intent),\n                    errorMessage: displayed.count !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.count) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Intent Distribution Validation\",\n                    errorMessage: \"Error calculating intent distribution: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate source performance metrics\n   */ static async validateSourcePerformance(displayedSources) {\n        try {\n            const results = [];\n            for (const displayed of displayedSources){\n                const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(displayed.source);\n                const actualCount = sourceDocuments.length;\n                results.push({\n                    isValid: displayed.documents === actualCount,\n                    expected: actualCount,\n                    actual: displayed.documents,\n                    description: \"Source Performance - \".concat(displayed.source, \" Document Count\"),\n                    errorMessage: displayed.documents !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.documents) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Source Performance Validation\",\n                    errorMessage: \"Error validating source performance: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate unique sources count using real GraphQL data\n   */ static async validateUniqueSourcesCount(displayedCount) {\n        try {\n            // Use the same GraphQL query that the dashboard uses\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n                query: GET_DASHBOARD_SUMMARY,\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            const actualCount = data.dashboardSummary.totalSources;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Sources Count (GraphQL)\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" from GraphQL but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Sources Count (GraphQL)\",\n                errorMessage: \"Error fetching GraphQL data: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate unique intent categories count\n   */ static async validateUniqueIntentCategoriesCount(displayedCount) {\n        try {\n            const uniqueCategories = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueIntentCategories();\n            const actualCount = uniqueCategories.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: \"Error fetching unique categories: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Calculate actual intent distribution from documents\n   */ static calculateIntentDistribution(documents) {\n        const intentCounts = {};\n        const totalDocuments = documents.length;\n        // Count occurrences of each intent category\n        documents.forEach((doc)=>{\n            doc.intentCategories.forEach((intent)=>{\n                intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n            });\n        });\n        // Convert to distribution format\n        return Object.entries(intentCounts).map((param)=>{\n            let [intent, count] = param;\n            return {\n                intent,\n                count,\n                percentage: totalDocuments > 0 ? count / totalDocuments * 100 : 0\n            };\n        }).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Run comprehensive validation of all dashboard counts with detailed logging\n   */ static async runComprehensiveValidation(dashboardData, logger) {\n        var _dashboardData_intentDistribution, _dashboardData_sourcePerformance;\n        const results = [];\n        const log = logger || (()=>{}); // No-op if no logger provided\n        log(\"info\", \"Starting comprehensive dashboard validation\");\n        log(\"info\", \"Dashboard data structure\", {\n            summaryKeys: Object.keys(dashboardData.summary || {}),\n            intentDistributionCount: ((_dashboardData_intentDistribution = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution === void 0 ? void 0 : _dashboardData_intentDistribution.length) || 0,\n            sourcePerformanceCount: ((_dashboardData_sourcePerformance = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance === void 0 ? void 0 : _dashboardData_sourcePerformance.length) || 0\n        });\n        try {\n            var _dashboardData_intentDistribution1, _dashboardData_sourcePerformance1;\n            // Validate total documents\n            log(\"info\", \"Validating total documents count\", {\n                displayedCount: dashboardData.summary.totalDocuments\n            });\n            const totalDocsResult = await this.validateTotalDocuments(dashboardData.summary.totalDocuments);\n            results.push(totalDocsResult);\n            log(totalDocsResult.isValid ? \"success\" : \"error\", \"Total documents validation: \".concat(totalDocsResult.isValid ? \"PASSED\" : \"FAILED\"), totalDocsResult);\n            // Validate intent distribution\n            log(\"info\", \"Validating intent distribution\", {\n                intentCount: ((_dashboardData_intentDistribution1 = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution1 === void 0 ? void 0 : _dashboardData_intentDistribution1.length) || 0\n            });\n            if (dashboardData.intentDistribution && dashboardData.intentDistribution.length > 0) {\n                const intentResults = await this.validateIntentDistribution(dashboardData.intentDistribution);\n                results.push(...intentResults);\n                const intentPassed = intentResults.filter((r)=>r.isValid).length;\n                log(intentPassed === intentResults.length ? \"success\" : \"warning\", \"Intent distribution validation: \".concat(intentPassed, \"/\").concat(intentResults.length, \" passed\"));\n            } else {\n                log(\"warning\", \"Intent distribution data is empty - skipping validation\");\n                results.push({\n                    isValid: false,\n                    expected: 1,\n                    actual: 0,\n                    description: \"Intent Distribution Data Availability\",\n                    errorMessage: \"No intent distribution data found in dashboard\"\n                });\n            }\n            // Validate source performance\n            log(\"info\", \"Validating source performance data\", {\n                sourceCount: ((_dashboardData_sourcePerformance1 = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance1 === void 0 ? void 0 : _dashboardData_sourcePerformance1.length) || 0\n            });\n            if (dashboardData.sourcePerformance && dashboardData.sourcePerformance.length > 0) {\n                const sourceResults = await this.validateSourcePerformance(dashboardData.sourcePerformance);\n                results.push(...sourceResults);\n                const sourcePassed = sourceResults.filter((r)=>r.isValid).length;\n                log(sourcePassed === sourceResults.length ? \"success\" : \"warning\", \"Source performance validation: \".concat(sourcePassed, \"/\").concat(sourceResults.length, \" passed\"));\n            } else {\n                log(\"warning\", \"Source performance data is empty - skipping validation\");\n                results.push({\n                    isValid: false,\n                    expected: 1,\n                    actual: 0,\n                    description: \"Source Performance Data Availability\",\n                    errorMessage: \"No source performance data found in dashboard\"\n                });\n            }\n            // Validate unique sources count\n            log(\"info\", \"Validating unique sources count\", {\n                displayedCount: dashboardData.summary.totalSources\n            });\n            const uniqueSourcesResult = await this.validateUniqueSourcesCount(dashboardData.summary.totalSources);\n            results.push(uniqueSourcesResult);\n            log(uniqueSourcesResult.isValid ? \"success\" : \"error\", \"Unique sources validation: \".concat(uniqueSourcesResult.isValid ? \"PASSED\" : \"FAILED\"), uniqueSourcesResult);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            log(\"error\", \"Critical validation error occurred\", {\n                error: errorMessage\n            });\n            results.push({\n                isValid: false,\n                expected: 0,\n                actual: 0,\n                description: \"Comprehensive Validation\",\n                errorMessage: \"Error during validation: \".concat(errorMessage)\n            });\n        }\n        const passedTests = results.filter((r)=>r.isValid).length;\n        const failedTests = results.filter((r)=>!r.isValid).length;\n        log(\"info\", \"Validation summary calculated\", {\n            totalTests: results.length,\n            passedTests,\n            failedTests,\n            overallValid: failedTests === 0\n        });\n        const report = {\n            timestamp: new Date().toISOString(),\n            overallValid: failedTests === 0,\n            validationResults: results,\n            summary: {\n                totalTests: results.length,\n                passedTests,\n                failedTests\n            }\n        };\n        log(\"success\", \"Comprehensive validation completed\", {\n            report\n        });\n        return report;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/countValidation.ts\n"));

/***/ })

});