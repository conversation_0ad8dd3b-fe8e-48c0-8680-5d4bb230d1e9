"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/countValidation.ts":
/*!**************************************!*\
  !*** ./src/utils/countValidation.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountValidator: function() { return /* binding */ CountValidator; }\n/* harmony export */ });\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/**\n * Count Validation Utilities\n * \n * This module provides comprehensive validation functions to ensure\n * data integrity and count accuracy across the FoodRegs dashboard.\n */ \n/**\n * Validates dashboard summary metrics against actual data\n */ class CountValidator {\n    /**\n   * Validate total document count\n   */ static async validateTotalDocuments(displayedCount) {\n        try {\n            const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getDocuments();\n            const actualCount = allDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Documents Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Documents Count\",\n                errorMessage: \"Error fetching documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate documents by source count\n   */ static async validateSourceDocumentCount(sourceName, displayedCount) {\n        try {\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getDocumentsBySource(sourceName);\n            const actualCount = sourceDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: \"Error fetching source documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate filtered document count\n   */ static async validateFilteredDocumentCount(filters, displayedCount) {\n        try {\n            const filteredDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getDocuments(filters);\n            const actualCount = filteredDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Filtered Documents Count (\".concat(JSON.stringify(filters), \")\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Filtered Documents Count\",\n                errorMessage: \"Error fetching filtered documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate intent category distribution counts\n   */ static async validateIntentDistribution(displayedDistribution) {\n        try {\n            const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getDocuments();\n            const actualDistribution = this.calculateIntentDistribution(allDocuments);\n            const results = [];\n            for (const displayed of displayedDistribution){\n                const actual = actualDistribution.find((a)=>a.intent === displayed.intent);\n                const actualCount = actual ? actual.count : 0;\n                results.push({\n                    isValid: displayed.count === actualCount,\n                    expected: actualCount,\n                    actual: displayed.count,\n                    description: \"Intent Distribution - \".concat(displayed.intent),\n                    errorMessage: displayed.count !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.count) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Intent Distribution Validation\",\n                    errorMessage: \"Error calculating intent distribution: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate source performance metrics\n   */ static async validateSourcePerformance(displayedSources) {\n        try {\n            const results = [];\n            for (const displayed of displayedSources){\n                const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getDocumentsBySource(displayed.source);\n                const actualCount = sourceDocuments.length;\n                results.push({\n                    isValid: displayed.documents === actualCount,\n                    expected: actualCount,\n                    actual: displayed.documents,\n                    description: \"Source Performance - \".concat(displayed.source, \" Document Count\"),\n                    errorMessage: displayed.documents !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.documents) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Source Performance Validation\",\n                    errorMessage: \"Error validating source performance: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate unique sources count\n   */ static async validateUniqueSourcesCount(displayedCount) {\n        try {\n            const uniqueSources = _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getUniqueSources();\n            const actualCount = uniqueSources.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Sources Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Sources Count\",\n                errorMessage: \"Error fetching unique sources: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate unique intent categories count\n   */ static async validateUniqueIntentCategoriesCount(displayedCount) {\n        try {\n            const uniqueCategories = _services_documentService__WEBPACK_IMPORTED_MODULE_0__.DocumentService.getUniqueIntentCategories();\n            const actualCount = uniqueCategories.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: \"Error fetching unique categories: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Calculate actual intent distribution from documents\n   */ static calculateIntentDistribution(documents) {\n        const intentCounts = {};\n        const totalDocuments = documents.length;\n        // Count occurrences of each intent category\n        documents.forEach((doc)=>{\n            doc.intentCategories.forEach((intent)=>{\n                intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n            });\n        });\n        // Convert to distribution format\n        return Object.entries(intentCounts).map((param)=>{\n            let [intent, count] = param;\n            return {\n                intent,\n                count,\n                percentage: totalDocuments > 0 ? count / totalDocuments * 100 : 0\n            };\n        }).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Run comprehensive validation of all dashboard counts with detailed logging\n   */ static async runComprehensiveValidation(dashboardData, logger) {\n        var _dashboardData_intentDistribution, _dashboardData_sourcePerformance;\n        const results = [];\n        const log = logger || (()=>{}); // No-op if no logger provided\n        log(\"info\", \"Starting comprehensive dashboard validation\");\n        log(\"info\", \"Dashboard data structure\", {\n            summaryKeys: Object.keys(dashboardData.summary || {}),\n            intentDistributionCount: ((_dashboardData_intentDistribution = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution === void 0 ? void 0 : _dashboardData_intentDistribution.length) || 0,\n            sourcePerformanceCount: ((_dashboardData_sourcePerformance = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance === void 0 ? void 0 : _dashboardData_sourcePerformance.length) || 0\n        });\n        try {\n            var _dashboardData_intentDistribution1, _dashboardData_sourcePerformance1;\n            // Validate total documents\n            log(\"info\", \"Validating total documents count\", {\n                displayedCount: dashboardData.summary.totalDocuments\n            });\n            const totalDocsResult = await this.validateTotalDocuments(dashboardData.summary.totalDocuments);\n            results.push(totalDocsResult);\n            log(totalDocsResult.isValid ? \"success\" : \"error\", \"Total documents validation: \".concat(totalDocsResult.isValid ? \"PASSED\" : \"FAILED\"), totalDocsResult);\n            // Validate intent distribution\n            log(\"info\", \"Validating intent distribution\", {\n                intentCount: ((_dashboardData_intentDistribution1 = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution1 === void 0 ? void 0 : _dashboardData_intentDistribution1.length) || 0\n            });\n            const intentResults = await this.validateIntentDistribution(dashboardData.intentDistribution);\n            results.push(...intentResults);\n            const intentPassed = intentResults.filter((r)=>r.isValid).length;\n            log(intentPassed === intentResults.length ? \"success\" : \"warning\", \"Intent distribution validation: \".concat(intentPassed, \"/\").concat(intentResults.length, \" passed\"));\n            // Validate source performance\n            log(\"info\", \"Validating source performance data\", {\n                sourceCount: ((_dashboardData_sourcePerformance1 = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance1 === void 0 ? void 0 : _dashboardData_sourcePerformance1.length) || 0\n            });\n            const sourceResults = await this.validateSourcePerformance(dashboardData.sourcePerformance);\n            results.push(...sourceResults);\n            const sourcePassed = sourceResults.filter((r)=>r.isValid).length;\n            log(sourcePassed === sourceResults.length ? \"success\" : \"warning\", \"Source performance validation: \".concat(sourcePassed, \"/\").concat(sourceResults.length, \" passed\"));\n            // Validate unique sources count\n            log(\"info\", \"Validating unique sources count\", {\n                displayedCount: dashboardData.summary.totalSources\n            });\n            const uniqueSourcesResult = await this.validateUniqueSourcesCount(dashboardData.summary.totalSources);\n            results.push(uniqueSourcesResult);\n            log(uniqueSourcesResult.isValid ? \"success\" : \"error\", \"Unique sources validation: \".concat(uniqueSourcesResult.isValid ? \"PASSED\" : \"FAILED\"), uniqueSourcesResult);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            log(\"error\", \"Critical validation error occurred\", {\n                error: errorMessage\n            });\n            results.push({\n                isValid: false,\n                expected: 0,\n                actual: 0,\n                description: \"Comprehensive Validation\",\n                errorMessage: \"Error during validation: \".concat(errorMessage)\n            });\n        }\n        const passedTests = results.filter((r)=>r.isValid).length;\n        const failedTests = results.filter((r)=>!r.isValid).length;\n        log(\"info\", \"Validation summary calculated\", {\n            totalTests: results.length,\n            passedTests,\n            failedTests,\n            overallValid: failedTests === 0\n        });\n        const report = {\n            timestamp: new Date().toISOString(),\n            overallValid: failedTests === 0,\n            validationResults: results,\n            summary: {\n                totalTests: results.length,\n                passedTests,\n                failedTests\n            }\n        };\n        log(\"success\", \"Comprehensive validation completed\", {\n            report\n        });\n        return report;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/countValidation.ts\n"));

/***/ })

});