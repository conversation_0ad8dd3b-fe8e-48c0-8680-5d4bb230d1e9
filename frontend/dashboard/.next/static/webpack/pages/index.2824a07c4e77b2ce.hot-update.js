"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/SuccessRateDetailView.tsx":
/*!**************************************************!*\
  !*** ./src/components/SuccessRateDetailView.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/**\n * SuccessRateDetailView - Detailed view of collection success rates and job performance\n * \n * Features:\n * - Job success/failure tracking\n * - Performance metrics over time\n * - Error analysis and categorization\n * - Retry and recovery statistics\n * - Source-specific performance breakdown\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n          query GetCollectionJobs($limit: Int, $offset: Int) {\\n            collectionJobs(limit: $limit, offset: $offset) {\\n              total\\n              jobs {\\n                id\\n                sourceId\\n                sourceName\\n                status\\n                startedAt\\n                completedAt\\n                documentsCollected\\n                errorMessage\\n                retryCount\\n                jobType\\n              }\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\nconst SuccessRateDetailView = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filteredJobs, setFilteredJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(20);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        status: \"\",\n        agency: \"\",\n        timeRange: \"24h\",\n        jobType: \"\",\n        search: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        fetchJobs();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        applyFilters();\n    }, [\n        jobs,\n        filters\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Set initial time on client side only\n        setCurrentTime(new Date().toLocaleTimeString());\n        // Update time every 30 seconds for job monitoring\n        const interval = setInterval(()=>{\n            setCurrentTime(new Date().toLocaleTimeString());\n            // Refresh running jobs\n            fetchJobs();\n        }, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const fetchJobs = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real collection jobs from GraphQL\n            const { data } = await apolloClient.query({\n                query: gql(_templateObject()),\n                variables: {\n                    limit: 50,\n                    offset: 0\n                },\n                fetchPolicy: \"network-only\"\n            });\n            const realJobs = data.collectionJobs.jobs.map((job)=>({\n                    id: job.id,\n                    sourceName: job.sourceName,\n                    agency: job.sourceName.split(\" \")[0],\n                    status: job.status.toLowerCase(),\n                    startTime: job.startedAt,\n                    endTime: job.completedAt,\n                    duration: job.completedAt ? Math.round((new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()) / 1000) : Math.round((new Date().getTime() - new Date(job.startedAt).getTime()) / 1000),\n                    documentsCollected: job.documentsCollected || 0,\n                    completion: job.status === \"completed\" ? 100 : job.status === \"failed\" ? 0 : Math.floor(Math.random() * 80) + 10,\n                    errorMessage: job.errorMessage,\n                    retryCount: job.retryCount || 0,\n                    jobType: job.jobType || \"scheduled\"\n                }));\n            setJobs(realJobs);\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...jobs\n        ];\n        // Apply time range filter\n        if (filters.timeRange !== \"all\") {\n            const now = new Date();\n            let cutoffTime;\n            switch(filters.timeRange){\n                case \"1h\":\n                    cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);\n                    break;\n                case \"24h\":\n                    cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n                    break;\n                case \"7d\":\n                    cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                    break;\n                default:\n                    cutoffTime = new Date(0);\n            }\n            filtered = filtered.filter((job)=>new Date(job.startTime) >= cutoffTime);\n        }\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            filtered = filtered.filter((job)=>{\n                var _job_errorMessage;\n                return job.sourceName.toLowerCase().includes(searchLower) || job.agency.toLowerCase().includes(searchLower) || ((_job_errorMessage = job.errorMessage) === null || _job_errorMessage === void 0 ? void 0 : _job_errorMessage.toLowerCase().includes(searchLower));\n            });\n        }\n        if (filters.status) {\n            filtered = filtered.filter((job)=>job.status === filters.status);\n        }\n        if (filters.agency) {\n            filtered = filtered.filter((job)=>job.agency === filters.agency);\n        }\n        if (filters.jobType) {\n            filtered = filtered.filter((job)=>job.jobType === filters.jobType);\n        }\n        setFilteredJobs(filtered);\n        setCurrentPage(1); // Reset to first page when filters change\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            status: \"\",\n            agency: \"\",\n            timeRange: \"24h\",\n            jobType: \"\",\n            search: \"\"\n        });\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CheckCircleIcon, {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 30\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XCircleIcon, {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 29\n                }, undefined);\n            case \"partial\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ExclamationTriangleIcon, {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 30\n                }, undefined);\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowPathIcon, {\n                    className: \"w-5 h-5 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 30\n                }, undefined);\n            case \"queued\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 29\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            case \"partial\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"running\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"queued\":\n                return \"bg-gray-100 text-gray-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 text-red-800\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"low\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const formatDuration = (ms)=>{\n        if (!ms) return \"N/A\";\n        const seconds = Math.floor(ms / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes % 60, \"m\");\n        if (minutes > 0) return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        return \"\".concat(seconds, \"s\");\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Calculate summary statistics\n    const totalJobs = filteredJobs.length;\n    const successfulJobs = filteredJobs.filter((j)=>j.status === \"success\").length;\n    const failedJobs = filteredJobs.filter((j)=>j.status === \"failed\").length;\n    const partialJobs = filteredJobs.filter((j)=>j.status === \"partial\").length;\n    const runningJobs = filteredJobs.filter((j)=>j.status === \"running\").length;\n    const successRate = totalJobs > 0 ? (successfulJobs / totalJobs * 100).toFixed(1) : \"0\";\n    // Pagination\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    const paginatedJobs = filteredJobs.slice(startIndex, endIndex);\n    const totalPages = Math.ceil(filteredJobs.length / pageSize);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Collection Jobs & Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"% success rate over \",\n                                            totalJobs,\n                                            \" jobs\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.FunnelIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Filters\",\n                                            showFilters ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChevronUpIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChevronDownIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 75\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-green-600\",\n                                        children: successfulJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Successful\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-red-600\",\n                                        children: failedJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Failed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-yellow-600\",\n                                        children: partialJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Partial\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-blue-600\",\n                                        children: runningJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Running\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        height: \"auto\",\n                        opacity: 1\n                    },\n                    exit: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    className: \"border-b border-gray-200 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: filters.search,\n                                                onChange: (e)=>handleFilterChange(\"search\", e.target.value),\n                                                placeholder: \"Search jobs...\",\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.status,\n                                                onChange: (e)=>handleFilterChange(\"status\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"success\",\n                                                        children: \"Successful\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"failed\",\n                                                        children: \"Failed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"partial\",\n                                                        children: \"Partial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"running\",\n                                                        children: \"Running\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"queued\",\n                                                        children: \"Queued\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Agency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.agency,\n                                                onChange: (e)=>handleFilterChange(\"agency\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Agencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"FDA\",\n                                                        children: \"FDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"USDA\",\n                                                        children: \"USDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"CFIA\",\n                                                        children: \"CFIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EFSA\",\n                                                        children: \"EFSA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EPA\",\n                                                        children: \"EPA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.timeRange,\n                                                onChange: (e)=>handleFilterChange(\"timeRange\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"1h\",\n                                                        children: \"Last Hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"24h\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Job Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.jobType,\n                                                onChange: (e)=>handleFilterChange(\"jobType\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"scheduled\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"manual\",\n                                                        children: \"Manual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"retry\",\n                                                        children: \"Retry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: clearFilters,\n                                        className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                        children: \"Clear all filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: currentTime && \"Last updated: \".concat(currentTime)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-200\",\n                children: paginatedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900\",\n                            children: \"No jobs found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500\",\n                            children: \"Try adjusting your search criteria or filters.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 11\n                }, undefined) : paginatedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"px-6 py-4 hover:bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-2\",\n                                            children: [\n                                                getStatusIcon(job.status),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: job.sourceName\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(job.status)),\n                                                    children: job.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getPriorityColor(job.priority)),\n                                                    children: job.priority\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                job.jobType === \"retry\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        \"Retry \",\n                                                        job.retryCount,\n                                                        \"/\",\n                                                        job.maxRetries\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Agency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.agency\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Started:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDate(job.startTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Duration:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDuration(job.duration)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.jobType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Documents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.documentsCollected,\n                                                        job.documentsExpected && \" / \".concat(job.documentsExpected)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Completion:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        job.documentsExpected ? \" \".concat(Math.round(job.documentsCollected / job.documentsExpected * 100), \"%\") : \" N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"End Time:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.endTime ? formatDate(job.endTime) : \"N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Job ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        job.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-red-600 bg-red-50 px-2 py-1 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Error:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    job.errorMessage,\n                                                    job.errorType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-red-100 text-red-800\",\n                                                        children: job.errorType\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        job.status === \"failed\" && job.retryCount < job.maxRetries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                            title: \"Retry Job\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowPathIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                            title: \"View Details\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 15\n                        }, undefined)\n                    }, job.id, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, undefined),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: startIndex + 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" to\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: Math.min(endIndex, filteredJobs.length)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" of\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage - 1),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage + 1),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 549,\n                columnNumber: 9\n            }, undefined),\n            filteredJobs.length > 0 && totalPages <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: currentTime && \"Last updated: \".concat(currentTime)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 584,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SuccessRateDetailView, \"hqVGK/e+zGTOgwgWEeMAAnkwFKo=\");\n_c = SuccessRateDetailView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SuccessRateDetailView);\nvar _c;\n$RefreshReg$(_c, \"SuccessRateDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SuccessRateDetailView.tsx\n"));

/***/ })

});