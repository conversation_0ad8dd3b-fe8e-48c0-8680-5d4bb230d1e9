"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardData {\\n    dashboardData {\\n      summary {\\n        totalSources\\n        activeSources\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        healthySources\\n        failingSources\\n        overallSuccessRate\\n        totalActions\\n        pendingActions\\n      }\\n      sourceMetrics {\\n        sourceId\\n        name\\n        agency\\n        totalDocuments\\n        successRate7d\\n        lastCollectionAt\\n        lastSuccessAt\\n      }\\n      intentMetrics {\\n        intent\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        avgConfidence\\n      }\\n      trendingTopics {\\n        topic\\n        mentions\\n        trend\\n        changePercent\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data\nconst GET_DASHBOARD_DATA = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Try to get real data from GraphQL API first\n        const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_DATA,\n            errorPolicy: \"all\"\n        });\n        if (data === null || data === void 0 ? void 0 : data.dashboardData) {\n            var _dashboardData_intentMetrics, _dashboardData_trendingTopics, _dashboardData_sourceMetrics;\n            const dashboardData = data.dashboardData;\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: dashboardData.summary.totalDocuments,\n                    documentsToday: dashboardData.summary.documents24h,\n                    activeSources: dashboardData.summary.activeSources,\n                    totalSources: dashboardData.summary.totalSources,\n                    healthySources: dashboardData.summary.healthySources,\n                    successRate: dashboardData.summary.overallSuccessRate,\n                    recentJobs: Math.floor(dashboardData.summary.totalDocuments * 0.1),\n                    pendingActions: dashboardData.summary.pendingActions,\n                    totalActions: dashboardData.summary.totalActions\n                },\n                intentDistribution: ((_dashboardData_intentMetrics = dashboardData.intentMetrics) === null || _dashboardData_intentMetrics === void 0 ? void 0 : _dashboardData_intentMetrics.map((intent, index)=>({\n                        intent: intent.intent,\n                        count: intent.totalDocuments,\n                        percentage: dashboardData.summary.totalDocuments > 0 ? intent.totalDocuments / dashboardData.summary.totalDocuments * 100 : 0,\n                        color: [\n                            \"#3B82F6\",\n                            \"#10B981\",\n                            \"#F59E0B\",\n                            \"#EF4444\",\n                            \"#8B5CF6\",\n                            \"#06B6D4\",\n                            \"#84CC16\",\n                            \"#F97316\"\n                        ][index % 8]\n                    }))) || [],\n                trendingTopics: ((_dashboardData_trendingTopics = dashboardData.trendingTopics) === null || _dashboardData_trendingTopics === void 0 ? void 0 : _dashboardData_trendingTopics.map((topic)=>({\n                        topic: topic.topic,\n                        mentions: topic.mentions,\n                        trend: topic.trend,\n                        change: topic.changePercent\n                    }))) || [],\n                sourcePerformance: ((_dashboardData_sourceMetrics = dashboardData.sourceMetrics) === null || _dashboardData_sourceMetrics === void 0 ? void 0 : _dashboardData_sourceMetrics.map((source)=>({\n                        source: source.name,\n                        agency: source.agency,\n                        status: source.successRate7d > 90 ? \"healthy\" : source.successRate7d > 70 ? \"warning\" : \"error\",\n                        documents: source.totalDocuments,\n                        successRate: source.successRate7d,\n                        lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : \"Never\",\n                        primaryIntent: \"Food Safety\" // Default for now\n                    }))) || []\n            };\n        }\n        // GraphQL data not available - return error instead of fallback\n        console.error(\"GraphQL dashboard data not available and no fallback allowed\");\n        throw new Error(\"Dashboard data unavailable. Please check your connection and ensure the backend is running.\");\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        throw new Error(\"Failed to generate dashboard data. Please check your connection and ensure the backend is running.\");\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSources();\n        if (dashboardData.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});