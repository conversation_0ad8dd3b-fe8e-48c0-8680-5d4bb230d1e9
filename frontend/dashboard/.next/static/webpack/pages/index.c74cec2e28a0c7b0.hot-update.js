"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/SuccessRateDetailView.tsx":
/*!**************************************************!*\
  !*** ./src/components/SuccessRateDetailView.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * SuccessRateDetailView - Detailed view of collection success rates and job performance\n * \n * Features:\n * - Job success/failure tracking\n * - Performance metrics over time\n * - Error analysis and categorization\n * - Retry and recovery statistics\n * - Source-specific performance breakdown\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n          query GetCollectionJobs($limit: Int, $offset: Int) {\\n            collectionJobs(limit: $limit, offset: $offset) {\\n              total\\n              jobs {\\n                id\\n                sourceId\\n                sourceName\\n                status\\n                startedAt\\n                completedAt\\n                documentsCollected\\n                errorMessage\\n                retryCount\\n                jobType\\n              }\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SuccessRateDetailView = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filteredJobs, setFilteredJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(20);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        status: \"\",\n        agency: \"\",\n        timeRange: \"24h\",\n        jobType: \"\",\n        search: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        fetchJobs();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        applyFilters();\n    }, [\n        jobs,\n        filters\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Set initial time on client side only\n        setCurrentTime(new Date().toLocaleTimeString());\n        // Update time every 30 seconds for job monitoring\n        const interval = setInterval(()=>{\n            setCurrentTime(new Date().toLocaleTimeString());\n            // Refresh running jobs\n            fetchJobs();\n        }, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const fetchJobs = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real collection jobs from GraphQL\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_3__.apolloClient.query({\n                query: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_4__.gql)(_templateObject()),\n                variables: {\n                    limit: 50,\n                    offset: 0\n                },\n                fetchPolicy: \"network-only\"\n            });\n            const realJobs = data.collectionJobs.jobs.map((job)=>({\n                    id: job.id,\n                    sourceName: job.sourceName,\n                    agency: job.sourceName.split(\" \")[0],\n                    status: job.status.toLowerCase(),\n                    startTime: job.startedAt,\n                    endTime: job.completedAt,\n                    duration: job.completedAt ? Math.round((new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()) / 1000) : Math.round((new Date().getTime() - new Date(job.startedAt).getTime()) / 1000),\n                    documentsCollected: job.documentsCollected || 0,\n                    completion: job.status === \"completed\" ? 100 : job.status === \"failed\" ? 0 : Math.floor(Math.random() * 80) + 10,\n                    errorMessage: job.errorMessage,\n                    retryCount: job.retryCount || 0,\n                    jobType: job.jobType || \"scheduled\"\n                }));\n            setJobs(realJobs);\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...jobs\n        ];\n        // Apply time range filter\n        if (filters.timeRange !== \"all\") {\n            const now = new Date();\n            let cutoffTime;\n            switch(filters.timeRange){\n                case \"1h\":\n                    cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);\n                    break;\n                case \"24h\":\n                    cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n                    break;\n                case \"7d\":\n                    cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                    break;\n                default:\n                    cutoffTime = new Date(0);\n            }\n            filtered = filtered.filter((job)=>new Date(job.startTime) >= cutoffTime);\n        }\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            filtered = filtered.filter((job)=>{\n                var _job_errorMessage;\n                return job.sourceName.toLowerCase().includes(searchLower) || job.agency.toLowerCase().includes(searchLower) || ((_job_errorMessage = job.errorMessage) === null || _job_errorMessage === void 0 ? void 0 : _job_errorMessage.toLowerCase().includes(searchLower));\n            });\n        }\n        if (filters.status) {\n            filtered = filtered.filter((job)=>job.status === filters.status);\n        }\n        if (filters.agency) {\n            filtered = filtered.filter((job)=>job.agency === filters.agency);\n        }\n        if (filters.jobType) {\n            filtered = filtered.filter((job)=>job.jobType === filters.jobType);\n        }\n        setFilteredJobs(filtered);\n        setCurrentPage(1); // Reset to first page when filters change\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            status: \"\",\n            agency: \"\",\n            timeRange: \"24h\",\n            jobType: \"\",\n            search: \"\"\n        });\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CheckCircleIcon, {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 30\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XCircleIcon, {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 29\n                }, undefined);\n            case \"partial\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ExclamationTriangleIcon, {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 30\n                }, undefined);\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowPathIcon, {\n                    className: \"w-5 h-5 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 30\n                }, undefined);\n            case \"queued\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 29\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            case \"partial\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"running\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"queued\":\n                return \"bg-gray-100 text-gray-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 text-red-800\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"low\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const formatDuration = (ms)=>{\n        if (!ms) return \"N/A\";\n        const seconds = Math.floor(ms / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes % 60, \"m\");\n        if (minutes > 0) return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        return \"\".concat(seconds, \"s\");\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Calculate summary statistics\n    const totalJobs = filteredJobs.length;\n    const successfulJobs = filteredJobs.filter((j)=>j.status === \"success\").length;\n    const failedJobs = filteredJobs.filter((j)=>j.status === \"failed\").length;\n    const partialJobs = filteredJobs.filter((j)=>j.status === \"partial\").length;\n    const runningJobs = filteredJobs.filter((j)=>j.status === \"running\").length;\n    const successRate = totalJobs > 0 ? (successfulJobs / totalJobs * 100).toFixed(1) : \"0\";\n    // Pagination\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    const paginatedJobs = filteredJobs.slice(startIndex, endIndex);\n    const totalPages = Math.ceil(filteredJobs.length / pageSize);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Collection Jobs & Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"% success rate over \",\n                                            totalJobs,\n                                            \" jobs\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.FunnelIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Filters\",\n                                            showFilters ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChevronUpIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 75\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-green-600\",\n                                        children: successfulJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Successful\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-red-600\",\n                                        children: failedJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Failed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-yellow-600\",\n                                        children: partialJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Partial\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-blue-600\",\n                                        children: runningJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Running\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        height: \"auto\",\n                        opacity: 1\n                    },\n                    exit: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    className: \"border-b border-gray-200 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: filters.search,\n                                                onChange: (e)=>handleFilterChange(\"search\", e.target.value),\n                                                placeholder: \"Search jobs...\",\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.status,\n                                                onChange: (e)=>handleFilterChange(\"status\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"success\",\n                                                        children: \"Successful\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"failed\",\n                                                        children: \"Failed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"partial\",\n                                                        children: \"Partial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"running\",\n                                                        children: \"Running\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"queued\",\n                                                        children: \"Queued\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Agency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.agency,\n                                                onChange: (e)=>handleFilterChange(\"agency\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Agencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"FDA\",\n                                                        children: \"FDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"USDA\",\n                                                        children: \"USDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"CFIA\",\n                                                        children: \"CFIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EFSA\",\n                                                        children: \"EFSA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EPA\",\n                                                        children: \"EPA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.timeRange,\n                                                onChange: (e)=>handleFilterChange(\"timeRange\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"1h\",\n                                                        children: \"Last Hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"24h\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Job Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.jobType,\n                                                onChange: (e)=>handleFilterChange(\"jobType\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"scheduled\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"manual\",\n                                                        children: \"Manual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"retry\",\n                                                        children: \"Retry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: clearFilters,\n                                        className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                        children: \"Clear all filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: currentTime && \"Last updated: \".concat(currentTime)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-200\",\n                children: paginatedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900\",\n                            children: \"No jobs found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500\",\n                            children: \"Try adjusting your search criteria or filters.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 11\n                }, undefined) : paginatedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"px-6 py-4 hover:bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-2\",\n                                            children: [\n                                                getStatusIcon(job.status),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: job.sourceName\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(job.status)),\n                                                    children: job.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getPriorityColor(job.priority)),\n                                                    children: job.priority\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                job.jobType === \"retry\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        \"Retry \",\n                                                        job.retryCount,\n                                                        \"/\",\n                                                        job.maxRetries\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Agency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.agency\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Started:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDate(job.startTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Duration:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDuration(job.duration)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.jobType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Documents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.documentsCollected,\n                                                        job.documentsExpected && \" / \".concat(job.documentsExpected)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Completion:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        job.documentsExpected ? \" \".concat(Math.round(job.documentsCollected / job.documentsExpected * 100), \"%\") : \" N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"End Time:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.endTime ? formatDate(job.endTime) : \"N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Job ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        job.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-red-600 bg-red-50 px-2 py-1 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Error:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    job.errorMessage,\n                                                    job.errorType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-red-100 text-red-800\",\n                                                        children: job.errorType\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        job.status === \"failed\" && job.retryCount < job.maxRetries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                            title: \"Retry Job\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowPathIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                            title: \"View Details\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 15\n                        }, undefined)\n                    }, job.id, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, undefined),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: startIndex + 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" to\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: Math.min(endIndex, filteredJobs.length)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" of\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage - 1),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage + 1),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 551,\n                columnNumber: 9\n            }, undefined),\n            filteredJobs.length > 0 && totalPages <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: currentTime && \"Last updated: \".concat(currentTime)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SuccessRateDetailView, \"hqVGK/e+zGTOgwgWEeMAAnkwFKo=\");\n_c = SuccessRateDetailView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SuccessRateDetailView);\nvar _c;\n$RefreshReg$(_c, \"SuccessRateDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SuccessRateDetailView.tsx\n"));

/***/ })

});