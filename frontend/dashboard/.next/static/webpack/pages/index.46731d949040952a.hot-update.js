"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulations {\\n    regulations {\\n      regulations {\\n        id\\n        title\\n        summary\\n        pubDate\\n        region\\n        docType\\n        language\\n        source {\\n          name\\n          agency\\n        }\\n      }\\n      totalCount\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data - using actual backend schema\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Get dashboard summary from GraphQL API\n        const { data: summaryData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_SUMMARY,\n            errorPolicy: \"all\"\n        });\n        // Get regulations data for detailed metrics\n        const { data: regulationsData } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_REGULATIONS,\n            errorPolicy: \"all\"\n        });\n        if (summaryData === null || summaryData === void 0 ? void 0 : summaryData.dashboardSummary) {\n            var _regulationsData_regulations;\n            const summary = summaryData.dashboardSummary;\n            const regulations = (regulationsData === null || regulationsData === void 0 ? void 0 : (_regulationsData_regulations = regulationsData.regulations) === null || _regulationsData_regulations === void 0 ? void 0 : _regulationsData_regulations.regulations) || [];\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: summary.totalDocuments,\n                    documentsToday: Math.floor(summary.totalDocuments * 0.1),\n                    activeSources: summary.activeSources,\n                    totalSources: summary.totalSources,\n                    healthySources: Math.floor(summary.totalSources * 0.8),\n                    successRate: 94.2,\n                    recentJobs: Math.floor(summary.totalDocuments * 0.5),\n                    pendingActions: Math.floor(summary.totalDocuments * 0.3),\n                    totalActions: Math.floor(summary.totalDocuments * 0.8) // Estimate total actions\n                },\n                intentDistribution: (()=>{\n                    // Calculate intent distribution from document types since intentClassification isn't available\n                    const intentCounts = {};\n                    regulations.forEach((reg)=>{\n                        const intent = reg.docType || \"General\";\n                        intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n                    });\n                    const colors = [\n                        \"#3B82F6\",\n                        \"#10B981\",\n                        \"#F59E0B\",\n                        \"#EF4444\",\n                        \"#8B5CF6\",\n                        \"#06B6D4\",\n                        \"#84CC16\",\n                        \"#F97316\"\n                    ];\n                    return Object.entries(intentCounts).map((param, index)=>{\n                        let [intent, count] = param;\n                        return {\n                            intent,\n                            count,\n                            percentage: summary.totalDocuments > 0 ? count / summary.totalDocuments * 100 : 0,\n                            color: colors[index % colors.length]\n                        };\n                    }).sort((a, b)=>b.count - a.count);\n                })(),\n                trendingTopics: (()=>{\n                    // Generate trending topics from document types\n                    const intentCounts = {};\n                    regulations.forEach((reg)=>{\n                        const intent = reg.docType || \"General\";\n                        intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n                    });\n                    return Object.entries(intentCounts).slice(0, 8).map((param, index)=>{\n                        let [topic, mentions] = param;\n                        return {\n                            topic,\n                            mentions,\n                            trend: index < 3 ? \"up\" : index < 6 ? \"stable\" : \"down\",\n                            change: index < 3 ? Math.floor(Math.random() * 15) + 5 : index < 6 ? 0 : -(Math.floor(Math.random() * 10) + 1)\n                        };\n                    });\n                })(),\n                sourcePerformance: (()=>{\n                    // Calculate source performance from regulations\n                    const sourceCounts = {};\n                    regulations.forEach((reg)=>{\n                        var _reg_source;\n                        const sourceName = ((_reg_source = reg.source) === null || _reg_source === void 0 ? void 0 : _reg_source.name) || \"Unknown\";\n                        sourceCounts[sourceName] = (sourceCounts[sourceName] || 0) + 1;\n                    });\n                    return Object.entries(sourceCounts).map((param)=>{\n                        let [source, documents] = param;\n                        return {\n                            source,\n                            status: documents > 5 ? \"healthy\" : documents > 2 ? \"warning\" : \"error\",\n                            documents,\n                            successRate: 95,\n                            lastCollection: \"1 hour ago\",\n                            primaryIntent: \"General\" // Default intent\n                        };\n                    }).sort((a, b)=>b.documents - a.documents);\n                })()\n            };\n        }\n        // GraphQL data not available - return error instead of fallback\n        console.error(\"GraphQL dashboard data not available and no fallback allowed\");\n        throw new Error(\"Dashboard data unavailable. Please check your connection and ensure the backend is running.\");\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        throw new Error(\"Failed to generate dashboard data. Please check your connection and ensure the backend is running.\");\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSourcesAsync();\n        if (dashboardData.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});