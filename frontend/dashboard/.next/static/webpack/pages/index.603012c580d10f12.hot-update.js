"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/SuccessRateDetailView.tsx":
/*!**************************************************!*\
  !*** ./src/components/SuccessRateDetailView.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,ExclamationTriangleIcon,FunnelIcon,XCircleIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/**\n * SuccessRateDetailView - Detailed view of collection success rates and job performance\n * \n * Features:\n * - Job success/failure tracking\n * - Performance metrics over time\n * - Error analysis and categorization\n * - Retry and recovery statistics\n * - Source-specific performance breakdown\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n          query GetCollectionJobs($limit: Int, $offset: Int) {\\n            collectionJobs(limit: $limit, offset: $offset) {\\n              total\\n              jobs {\\n                id\\n                sourceId\\n                sourceName\\n                status\\n                startedAt\\n                completedAt\\n                documentsCollected\\n                errorMessage\\n                retryCount\\n                jobType\\n              }\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\nconst SuccessRateDetailView = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filteredJobs, setFilteredJobs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(20);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        status: \"\",\n        agency: \"\",\n        timeRange: \"24h\",\n        jobType: \"\",\n        search: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        fetchJobs();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        applyFilters();\n    }, [\n        jobs,\n        filters\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Set initial time on client side only\n        setCurrentTime(new Date().toLocaleTimeString());\n        // Update time every 30 seconds for job monitoring\n        const interval = setInterval(()=>{\n            setCurrentTime(new Date().toLocaleTimeString());\n            // Refresh running jobs\n            fetchJobs();\n        }, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const fetchJobs = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real collection jobs from GraphQL\n            const { data } = await apolloClient.query({\n                query: gql(_templateObject()),\n                variables: {\n                    limit: 50,\n                    offset: 0\n                },\n                fetchPolicy: \"network-only\"\n            });\n            const realJobs = data.collectionJobs.jobs.map((job)=>({\n                    id: job.id,\n                    sourceName: job.sourceName,\n                    agency: job.sourceName.split(\" \")[0],\n                    status: job.status.toLowerCase(),\n                    startTime: job.startedAt,\n                    endTime: job.completedAt,\n                    duration: job.completedAt ? Math.round((new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()) / 1000) : Math.round((new Date().getTime() - new Date(job.startedAt).getTime()) / 1000),\n                    documentsCollected: job.documentsCollected || 0,\n                    completion: job.status === \"completed\" ? 100 : job.status === \"failed\" ? 0 : Math.floor(Math.random() * 80) + 10,\n                    errorMessage: job.errorMessage,\n                    retryCount: job.retryCount || 0,\n                    jobType: job.jobType || \"scheduled\"\n                }));\n            setJobs(realJobs);\n            const sources = [\n                {\n                    name: \"FDA Food Safety\",\n                    agency: \"FDA\"\n                },\n                {\n                    name: \"USDA FSIS\",\n                    agency: \"USDA\"\n                },\n                {\n                    name: \"CFIA Newsroom\",\n                    agency: \"CFIA\"\n                },\n                {\n                    name: \"EFSA News\",\n                    agency: \"EFSA\"\n                },\n                {\n                    name: \"EPA Food Safety\",\n                    agency: \"EPA\"\n                }\n            ];\n            const statuses = [\n                \"success\",\n                \"failed\",\n                \"partial\",\n                \"running\",\n                \"queued\"\n            ];\n            const jobTypes = [\n                \"scheduled\",\n                \"manual\",\n                \"retry\"\n            ];\n            const priorities = [\n                \"high\",\n                \"medium\",\n                \"low\"\n            ];\n            for(let i = 1; i <= 50; i++){\n                const source = sources[Math.floor(Math.random() * sources.length)];\n                const status = statuses[Math.floor(Math.random() * statuses.length)];\n                const jobType = jobTypes[Math.floor(Math.random() * jobTypes.length)];\n                const priority = priorities[Math.floor(Math.random() * priorities.length)];\n                const startTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);\n                const duration = status === \"running\" ? undefined : Math.floor(Math.random() * 300000) + 30000; // 30s to 5min\n                const endTime = duration ? new Date(startTime.getTime() + duration) : undefined;\n                const documentsExpected = Math.floor(Math.random() * 100) + 10;\n                let documentsCollected = 0;\n                switch(status){\n                    case \"success\":\n                        documentsCollected = documentsExpected;\n                        break;\n                    case \"partial\":\n                        documentsCollected = Math.floor(documentsExpected * (0.3 + Math.random() * 0.6));\n                        break;\n                    case \"failed\":\n                        documentsCollected = 0;\n                        break;\n                    case \"running\":\n                        documentsCollected = Math.floor(documentsExpected * Math.random() * 0.8);\n                        break;\n                    case \"queued\":\n                        documentsCollected = 0;\n                        break;\n                }\n                const errorMessages = [\n                    \"HTTP 503 - Service temporarily unavailable\",\n                    \"Connection timeout after 30 seconds\",\n                    \"Authentication failed - invalid credentials\",\n                    \"Rate limit exceeded - too many requests\",\n                    \"SSL certificate verification failed\",\n                    \"Invalid XML/JSON response format\",\n                    \"Server returned empty response\",\n                    \"DNS resolution failed\"\n                ];\n                mockJobs.push({\n                    id: i.toString(),\n                    sourceName: source.name,\n                    sourceId: \"source-\".concat(i % 5 + 1),\n                    agency: source.agency,\n                    status,\n                    startTime: startTime.toISOString(),\n                    endTime: endTime === null || endTime === void 0 ? void 0 : endTime.toISOString(),\n                    duration,\n                    documentsCollected,\n                    documentsExpected,\n                    errorMessage: status === \"failed\" || status === \"partial\" ? errorMessages[Math.floor(Math.random() * errorMessages.length)] : undefined,\n                    errorType: status === \"failed\" || status === \"partial\" ? [\n                        \"network\",\n                        \"authentication\",\n                        \"parsing\",\n                        \"timeout\"\n                    ][Math.floor(Math.random() * 4)] : undefined,\n                    retryCount: jobType === \"retry\" ? Math.floor(Math.random() * 3) + 1 : 0,\n                    maxRetries: 3,\n                    jobType,\n                    priority\n                });\n            }\n            // Sort by start time (newest first)\n            mockJobs.sort((a, b)=>new Date(b.startTime).getTime() - new Date(a.startTime).getTime());\n            setJobs(mockJobs);\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...jobs\n        ];\n        // Apply time range filter\n        if (filters.timeRange !== \"all\") {\n            const now = new Date();\n            let cutoffTime;\n            switch(filters.timeRange){\n                case \"1h\":\n                    cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);\n                    break;\n                case \"24h\":\n                    cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n                    break;\n                case \"7d\":\n                    cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                    break;\n                default:\n                    cutoffTime = new Date(0);\n            }\n            filtered = filtered.filter((job)=>new Date(job.startTime) >= cutoffTime);\n        }\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            filtered = filtered.filter((job)=>{\n                var _job_errorMessage;\n                return job.sourceName.toLowerCase().includes(searchLower) || job.agency.toLowerCase().includes(searchLower) || ((_job_errorMessage = job.errorMessage) === null || _job_errorMessage === void 0 ? void 0 : _job_errorMessage.toLowerCase().includes(searchLower));\n            });\n        }\n        if (filters.status) {\n            filtered = filtered.filter((job)=>job.status === filters.status);\n        }\n        if (filters.agency) {\n            filtered = filtered.filter((job)=>job.agency === filters.agency);\n        }\n        if (filters.jobType) {\n            filtered = filtered.filter((job)=>job.jobType === filters.jobType);\n        }\n        setFilteredJobs(filtered);\n        setCurrentPage(1); // Reset to first page when filters change\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            status: \"\",\n            agency: \"\",\n            timeRange: \"24h\",\n            jobType: \"\",\n            search: \"\"\n        });\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CheckCircleIcon, {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 30\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XCircleIcon, {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 29\n                }, undefined);\n            case \"partial\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ExclamationTriangleIcon, {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 30\n                }, undefined);\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowPathIcon, {\n                    className: \"w-5 h-5 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 30\n                }, undefined);\n            case \"queued\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 29\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            case \"partial\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"running\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"queued\":\n                return \"bg-gray-100 text-gray-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 text-red-800\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"low\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const formatDuration = (ms)=>{\n        if (!ms) return \"N/A\";\n        const seconds = Math.floor(ms / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes % 60, \"m\");\n        if (minutes > 0) return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        return \"\".concat(seconds, \"s\");\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Calculate summary statistics\n    const totalJobs = filteredJobs.length;\n    const successfulJobs = filteredJobs.filter((j)=>j.status === \"success\").length;\n    const failedJobs = filteredJobs.filter((j)=>j.status === \"failed\").length;\n    const partialJobs = filteredJobs.filter((j)=>j.status === \"partial\").length;\n    const runningJobs = filteredJobs.filter((j)=>j.status === \"running\").length;\n    const successRate = totalJobs > 0 ? (successfulJobs / totalJobs * 100).toFixed(1) : \"0\";\n    // Pagination\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    const paginatedJobs = filteredJobs.slice(startIndex, endIndex);\n    const totalPages = Math.ceil(filteredJobs.length / pageSize);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Collection Jobs & Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"% success rate over \",\n                                            totalJobs,\n                                            \" jobs\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.FunnelIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Filters\",\n                                            showFilters ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChevronUpIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChevronDownIcon, {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 75\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-green-600\",\n                                        children: successfulJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Successful\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-red-600\",\n                                        children: failedJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Failed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-yellow-600\",\n                                        children: partialJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Partial\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-blue-600\",\n                                        children: runningJobs\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Running\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-600\",\n                                        children: [\n                                            successRate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Success Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        height: \"auto\",\n                        opacity: 1\n                    },\n                    exit: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    className: \"border-b border-gray-200 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: filters.search,\n                                                onChange: (e)=>handleFilterChange(\"search\", e.target.value),\n                                                placeholder: \"Search jobs...\",\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.status,\n                                                onChange: (e)=>handleFilterChange(\"status\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"success\",\n                                                        children: \"Successful\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"failed\",\n                                                        children: \"Failed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"partial\",\n                                                        children: \"Partial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"running\",\n                                                        children: \"Running\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"queued\",\n                                                        children: \"Queued\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Agency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.agency,\n                                                onChange: (e)=>handleFilterChange(\"agency\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Agencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"FDA\",\n                                                        children: \"FDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"USDA\",\n                                                        children: \"USDA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"CFIA\",\n                                                        children: \"CFIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EFSA\",\n                                                        children: \"EFSA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"EPA\",\n                                                        children: \"EPA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.timeRange,\n                                                onChange: (e)=>handleFilterChange(\"timeRange\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"1h\",\n                                                        children: \"Last Hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"24h\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Job Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"select\", {\n                                                value: filters.jobType,\n                                                onChange: (e)=>handleFilterChange(\"jobType\", e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"scheduled\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"manual\",\n                                                        children: \"Manual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"option\", {\n                                                        value: \"retry\",\n                                                        children: \"Retry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                        onClick: clearFilters,\n                                        className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                        children: \"Clear all filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: currentTime && \"Last updated: \".concat(currentTime)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-200\",\n                children: paginatedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900\",\n                            children: \"No jobs found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500\",\n                            children: \"Try adjusting your search criteria or filters.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, undefined) : paginatedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"px-6 py-4 hover:bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-2\",\n                                            children: [\n                                                getStatusIcon(job.status),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: job.sourceName\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(job.status)),\n                                                    children: job.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getPriorityColor(job.priority)),\n                                                    children: job.priority\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                job.jobType === \"retry\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        \"Retry \",\n                                                        job.retryCount,\n                                                        \"/\",\n                                                        job.maxRetries\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Agency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.agency\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Started:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDate(job.startTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Duration:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        formatDuration(job.duration)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.jobType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Documents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.documentsCollected,\n                                                        job.documentsExpected && \" / \".concat(job.documentsExpected)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Completion:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        job.documentsExpected ? \" \".concat(Math.round(job.documentsCollected / job.documentsExpected * 100), \"%\") : \" N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"End Time:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.endTime ? formatDate(job.endTime) : \"N/A\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Job ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        job.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        job.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-red-600 bg-red-50 px-2 py-1 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Error:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    job.errorMessage,\n                                                    job.errorType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-red-100 text-red-800\",\n                                                        children: job.errorType\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        job.status === \"failed\" && job.retryCount < job.maxRetries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                            title: \"Retry Job\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowPathIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                            title: \"View Details\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_ExclamationTriangleIcon_FunnelIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 15\n                        }, undefined)\n                    }, job.id, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 519,\n                columnNumber: 7\n            }, undefined),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: startIndex + 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" to\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: Math.min(endIndex, filteredJobs.length)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" of\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage - 1),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(currentPage + 1),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 629,\n                columnNumber: 9\n            }, undefined),\n            filteredJobs.length > 0 && totalPages <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: filteredJobs.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" jobs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: currentTime && \"Last updated: \".concat(currentTime)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n                lineNumber: 664,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SuccessRateDetailView.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SuccessRateDetailView, \"hqVGK/e+zGTOgwgWEeMAAnkwFKo=\");\n_c = SuccessRateDetailView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SuccessRateDetailView);\nvar _c;\n$RefreshReg$(_c, \"SuccessRateDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SuccessRateDetailView.tsx\n"));

/***/ })

});