"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardData {\\n    dashboardData {\\n      summary {\\n        totalSources\\n        activeSources\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        healthySources\\n        failingSources\\n        overallSuccessRate\\n        totalActions\\n        pendingActions\\n      }\\n      sourceMetrics {\\n        sourceId\\n        name\\n        agency\\n        totalDocuments\\n        successRate7d\\n        lastCollectionAt\\n        lastSuccessAt\\n      }\\n      intentMetrics {\\n        intent\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        avgConfidence\\n      }\\n      trendingTopics {\\n        topic\\n        mentions\\n        trend\\n        changePercent\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data\nconst GET_DASHBOARD_DATA = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Try to get real data from GraphQL API first\n        const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_DATA,\n            errorPolicy: \"all\"\n        });\n        if (data === null || data === void 0 ? void 0 : data.dashboardData) {\n            var _dashboardData_intentMetrics, _dashboardData_trendingTopics, _dashboardData_sourceMetrics;\n            const dashboardData = data.dashboardData;\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: dashboardData.summary.totalDocuments,\n                    documentsToday: dashboardData.summary.documents24h,\n                    activeSources: dashboardData.summary.activeSources,\n                    totalSources: dashboardData.summary.totalSources,\n                    healthySources: dashboardData.summary.healthySources,\n                    successRate: dashboardData.summary.overallSuccessRate,\n                    recentJobs: Math.floor(dashboardData.summary.totalDocuments * 0.1),\n                    pendingActions: dashboardData.summary.pendingActions,\n                    totalActions: dashboardData.summary.totalActions\n                },\n                intentDistribution: ((_dashboardData_intentMetrics = dashboardData.intentMetrics) === null || _dashboardData_intentMetrics === void 0 ? void 0 : _dashboardData_intentMetrics.map((intent, index)=>({\n                        intent: intent.intent,\n                        count: intent.totalDocuments,\n                        percentage: dashboardData.summary.totalDocuments > 0 ? intent.totalDocuments / dashboardData.summary.totalDocuments * 100 : 0,\n                        color: [\n                            \"#3B82F6\",\n                            \"#10B981\",\n                            \"#F59E0B\",\n                            \"#EF4444\",\n                            \"#8B5CF6\",\n                            \"#06B6D4\",\n                            \"#84CC16\",\n                            \"#F97316\"\n                        ][index % 8]\n                    }))) || [],\n                trendingTopics: ((_dashboardData_trendingTopics = dashboardData.trendingTopics) === null || _dashboardData_trendingTopics === void 0 ? void 0 : _dashboardData_trendingTopics.map((topic)=>({\n                        topic: topic.topic,\n                        mentions: topic.mentions,\n                        trend: topic.trend,\n                        change: topic.changePercent\n                    }))) || [],\n                sourcePerformance: ((_dashboardData_sourceMetrics = dashboardData.sourceMetrics) === null || _dashboardData_sourceMetrics === void 0 ? void 0 : _dashboardData_sourceMetrics.map((source)=>({\n                        source: source.name,\n                        agency: source.agency,\n                        status: source.successRate7d > 90 ? \"healthy\" : source.successRate7d > 70 ? \"warning\" : \"error\",\n                        documents: source.totalDocuments,\n                        successRate: source.successRate7d,\n                        lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : \"Never\",\n                        primaryIntent: \"Food Safety\" // Default for now\n                    }))) || []\n            };\n        }\n        // GraphQL data not available - return error instead of fallback\n        console.error(\"GraphQL dashboard data not available and no fallback allowed\");\n        throw new Error(\"Dashboard data unavailable. Please check your connection and ensure the backend is running.\");\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        throw new Error(\"Failed to generate dashboard data. Please check your connection and ensure the backend is running.\");\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSourcesAsync();\n        if (dashboardData.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});