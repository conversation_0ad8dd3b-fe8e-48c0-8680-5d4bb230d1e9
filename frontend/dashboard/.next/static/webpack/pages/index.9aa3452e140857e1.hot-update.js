"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/countValidation.ts":
/*!**************************************!*\
  !*** ./src/utils/countValidation.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountValidator: function() { return /* binding */ CountValidator; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Count Validation Utilities\n * \n * This module provides comprehensive validation functions to ensure\n * data integrity and count accuracy across the FoodRegs dashboard.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulationsCount {\\n    regulations {\\n      total\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL queries to match dashboard data sources\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS_COUNT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Validates dashboard summary metrics against actual data\n */ class CountValidator {\n    /**\n   * Validate total document count using real GraphQL data\n   */ static async validateTotalDocuments(displayedCount) {\n        try {\n            // Use the same GraphQL query that the dashboard uses\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n                query: GET_DASHBOARD_SUMMARY,\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            const actualCount = data.dashboardSummary.totalDocuments;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" from GraphQL but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: \"Error fetching GraphQL data: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate documents by source count\n   */ static async validateSourceDocumentCount(sourceName, displayedCount) {\n        try {\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourceName);\n            const actualCount = sourceDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: \"Error fetching source documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate filtered document count\n   */ static async validateFilteredDocumentCount(filters, displayedCount) {\n        try {\n            const filteredDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments(filters);\n            const actualCount = filteredDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Filtered Documents Count (\".concat(JSON.stringify(filters), \")\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Filtered Documents Count\",\n                errorMessage: \"Error fetching filtered documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate intent category distribution counts\n   */ static async validateIntentDistribution(displayedDistribution) {\n        try {\n            const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n            const actualDistribution = this.calculateIntentDistribution(allDocuments);\n            const results = [];\n            for (const displayed of displayedDistribution){\n                const actual = actualDistribution.find((a)=>a.intent === displayed.intent);\n                const actualCount = actual ? actual.count : 0;\n                results.push({\n                    isValid: displayed.count === actualCount,\n                    expected: actualCount,\n                    actual: displayed.count,\n                    description: \"Intent Distribution - \".concat(displayed.intent),\n                    errorMessage: displayed.count !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.count) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Intent Distribution Validation\",\n                    errorMessage: \"Error calculating intent distribution: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate source performance metrics\n   */ static async validateSourcePerformance(displayedSources) {\n        try {\n            const results = [];\n            for (const displayed of displayedSources){\n                const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(displayed.source);\n                const actualCount = sourceDocuments.length;\n                results.push({\n                    isValid: displayed.documents === actualCount,\n                    expected: actualCount,\n                    actual: displayed.documents,\n                    description: \"Source Performance - \".concat(displayed.source, \" Document Count\"),\n                    errorMessage: displayed.documents !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.documents) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Source Performance Validation\",\n                    errorMessage: \"Error validating source performance: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate unique sources count\n   */ static async validateUniqueSourcesCount(displayedCount) {\n        try {\n            const uniqueSources = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSources();\n            const actualCount = uniqueSources.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Sources Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Sources Count\",\n                errorMessage: \"Error fetching unique sources: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate unique intent categories count\n   */ static async validateUniqueIntentCategoriesCount(displayedCount) {\n        try {\n            const uniqueCategories = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueIntentCategories();\n            const actualCount = uniqueCategories.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: \"Error fetching unique categories: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Calculate actual intent distribution from documents\n   */ static calculateIntentDistribution(documents) {\n        const intentCounts = {};\n        const totalDocuments = documents.length;\n        // Count occurrences of each intent category\n        documents.forEach((doc)=>{\n            doc.intentCategories.forEach((intent)=>{\n                intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n            });\n        });\n        // Convert to distribution format\n        return Object.entries(intentCounts).map((param)=>{\n            let [intent, count] = param;\n            return {\n                intent,\n                count,\n                percentage: totalDocuments > 0 ? count / totalDocuments * 100 : 0\n            };\n        }).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Run comprehensive validation of all dashboard counts with detailed logging\n   */ static async runComprehensiveValidation(dashboardData, logger) {\n        var _dashboardData_intentDistribution, _dashboardData_sourcePerformance;\n        const results = [];\n        const log = logger || (()=>{}); // No-op if no logger provided\n        log(\"info\", \"Starting comprehensive dashboard validation\");\n        log(\"info\", \"Dashboard data structure\", {\n            summaryKeys: Object.keys(dashboardData.summary || {}),\n            intentDistributionCount: ((_dashboardData_intentDistribution = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution === void 0 ? void 0 : _dashboardData_intentDistribution.length) || 0,\n            sourcePerformanceCount: ((_dashboardData_sourcePerformance = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance === void 0 ? void 0 : _dashboardData_sourcePerformance.length) || 0\n        });\n        try {\n            var _dashboardData_intentDistribution1, _dashboardData_sourcePerformance1;\n            // Validate total documents\n            log(\"info\", \"Validating total documents count\", {\n                displayedCount: dashboardData.summary.totalDocuments\n            });\n            const totalDocsResult = await this.validateTotalDocuments(dashboardData.summary.totalDocuments);\n            results.push(totalDocsResult);\n            log(totalDocsResult.isValid ? \"success\" : \"error\", \"Total documents validation: \".concat(totalDocsResult.isValid ? \"PASSED\" : \"FAILED\"), totalDocsResult);\n            // Validate intent distribution\n            log(\"info\", \"Validating intent distribution\", {\n                intentCount: ((_dashboardData_intentDistribution1 = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution1 === void 0 ? void 0 : _dashboardData_intentDistribution1.length) || 0\n            });\n            const intentResults = await this.validateIntentDistribution(dashboardData.intentDistribution);\n            results.push(...intentResults);\n            const intentPassed = intentResults.filter((r)=>r.isValid).length;\n            log(intentPassed === intentResults.length ? \"success\" : \"warning\", \"Intent distribution validation: \".concat(intentPassed, \"/\").concat(intentResults.length, \" passed\"));\n            // Validate source performance\n            log(\"info\", \"Validating source performance data\", {\n                sourceCount: ((_dashboardData_sourcePerformance1 = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance1 === void 0 ? void 0 : _dashboardData_sourcePerformance1.length) || 0\n            });\n            const sourceResults = await this.validateSourcePerformance(dashboardData.sourcePerformance);\n            results.push(...sourceResults);\n            const sourcePassed = sourceResults.filter((r)=>r.isValid).length;\n            log(sourcePassed === sourceResults.length ? \"success\" : \"warning\", \"Source performance validation: \".concat(sourcePassed, \"/\").concat(sourceResults.length, \" passed\"));\n            // Validate unique sources count\n            log(\"info\", \"Validating unique sources count\", {\n                displayedCount: dashboardData.summary.totalSources\n            });\n            const uniqueSourcesResult = await this.validateUniqueSourcesCount(dashboardData.summary.totalSources);\n            results.push(uniqueSourcesResult);\n            log(uniqueSourcesResult.isValid ? \"success\" : \"error\", \"Unique sources validation: \".concat(uniqueSourcesResult.isValid ? \"PASSED\" : \"FAILED\"), uniqueSourcesResult);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            log(\"error\", \"Critical validation error occurred\", {\n                error: errorMessage\n            });\n            results.push({\n                isValid: false,\n                expected: 0,\n                actual: 0,\n                description: \"Comprehensive Validation\",\n                errorMessage: \"Error during validation: \".concat(errorMessage)\n            });\n        }\n        const passedTests = results.filter((r)=>r.isValid).length;\n        const failedTests = results.filter((r)=>!r.isValid).length;\n        log(\"info\", \"Validation summary calculated\", {\n            totalTests: results.length,\n            passedTests,\n            failedTests,\n            overallValid: failedTests === 0\n        });\n        const report = {\n            timestamp: new Date().toISOString(),\n            overallValid: failedTests === 0,\n            validationResults: results,\n            summary: {\n                totalTests: results.length,\n                passedTests,\n                failedTests\n            }\n        };\n        log(\"success\", \"Comprehensive validation completed\", {\n            report\n        });\n        return report;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/countValidation.ts\n"));

/***/ })

});