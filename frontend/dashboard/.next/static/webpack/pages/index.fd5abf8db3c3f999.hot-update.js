/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/DocumentViewer.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentViewer.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,BookmarkIcon,BuildingOfficeIcon,CalendarIcon,DocumentTextIcon,GlobeAltIcon,ShareIcon,TargetIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,BookmarkIcon,BuildingOfficeIcon,CalendarIcon,DocumentTextIcon,GlobeAltIcon,ShareIcon,TargetIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_services_documentService__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst DocumentViewer = (param)=>{\n    let { documentId, onBack } = param;\n    _s();\n    const [document, setDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadDocument = async ()=>{\n            try {\n                setLoading(true);\n                const doc = await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.DocumentService.getDocument(documentId);\n                if (doc) {\n                    setDocument(doc);\n                } else {\n                    setError(\"Document not found\");\n                }\n            } catch (err) {\n                setError(\"Error loading document\");\n                console.error(\"Error loading document:\", err);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadDocument();\n    }, [\n        documentId\n    ]);\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 90) return \"text-green-600 bg-green-50\";\n        if (confidence >= 75) return \"text-yellow-600 bg-yellow-50\";\n        return \"text-red-600 bg-red-50\";\n    };\n    const getRegionFlag = (region)=>{\n        switch(region){\n            case \"US\":\n                return \"\\uD83C\\uDDFA\\uD83C\\uDDF8\";\n            case \"CA\":\n                return \"\\uD83C\\uDDE8\\uD83C\\uDDE6\";\n            case \"EU\":\n                return \"\\uD83C\\uDDEA\\uD83C\\uDDFA\";\n            default:\n                return \"\\uD83C\\uDF0D\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading document...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error || !document) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon, {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Document Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error || \"The requested document could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowLeftIcon, {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Back to Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        exit: {\n            opacity: 0\n        },\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 sticky top-0 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onBack,\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowLeftIcon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.BookmarkIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShareIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowDownTrayIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.1\n                        },\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: document.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-lg leading-relaxed\",\n                                                children: document.summary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-6 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: getRegionFlag(document.region)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getConfidenceColor(document.confidence)),\n                                                children: [\n                                                    document.confidence,\n                                                    \"% confidence\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.BuildingOfficeIcon, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Source\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: document.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: new Date(document.date).toLocaleDateString(\"en-US\", {\n                                                            year: \"numeric\",\n                                                            month: \"long\",\n                                                            day: \"numeric\"\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: document.type\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.GlobeAltIcon, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Region\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: document.region\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TargetIcon, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 font-medium\",\n                                                children: \"Intent Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: document.intentCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-blue-50 text-blue-700 text-xs font-medium rounded-md\",\n                                                children: category\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-gray max-w-none\",\n                            children: document.content ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: document.content\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon, {\n                                        className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Document Content Loading\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-4\",\n                                        children: [\n                                            \"The full document content is being loaded from: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 67\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"text-sm bg-gray-100 px-2 py-1 rounded\",\n                                                children: document.filePath\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"In a production environment, this would display the complete regulatory document content.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Related Documents\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_BookmarkIcon_BuildingOfficeIcon_CalendarIcon_DocumentTextIcon_GlobeAltIcon_ShareIcon_TargetIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TrendingUpIcon, {\n                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Related documents will be shown here based on content similarity and regulatory connections.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/DocumentViewer.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentViewer, \"QoN1s5BGGaCLwapJLfZMLZQcPLo=\");\n_c = DocumentViewer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentViewer);\nvar _c;\n$RefreshReg$(_c, \"DocumentViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9Eb2N1bWVudFZpZXdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUQ7QUFDWjtBQVlGO0FBQzZDO0FBT2xGLE1BQU1lLGlCQUFnRDtRQUFDLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFOztJQUMzRSxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2xCLCtDQUFRQSxDQUE0QjtJQUNwRSxNQUFNLENBQUNtQixTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNxQixPQUFPQyxTQUFTLEdBQUd0QiwrQ0FBUUEsQ0FBZ0I7SUFFbERDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXNCLGVBQWU7WUFDbkIsSUFBSTtnQkFDRkgsV0FBVztnQkFDWCxNQUFNSSxNQUFNLE1BQU1YLHNFQUFlQSxDQUFDWSxXQUFXLENBQUNWO2dCQUM5QyxJQUFJUyxLQUFLO29CQUNQTixZQUFZTTtnQkFDZCxPQUFPO29CQUNMRixTQUFTO2dCQUNYO1lBQ0YsRUFBRSxPQUFPSSxLQUFLO2dCQUNaSixTQUFTO2dCQUNUSyxRQUFRTixLQUFLLENBQUMsMkJBQTJCSztZQUMzQyxTQUFVO2dCQUNSTixXQUFXO1lBQ2I7UUFDRjtRQUVBRztJQUNGLEdBQUc7UUFBQ1I7S0FBVztJQUVmLE1BQU1hLHFCQUFxQixDQUFDQztRQUMxQixJQUFJQSxjQUFjLElBQUksT0FBTztRQUM3QixJQUFJQSxjQUFjLElBQUksT0FBTztRQUM3QixPQUFPO0lBQ1Q7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQ0M7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUFNLE9BQU87WUFDbEIsS0FBSztnQkFBTSxPQUFPO1lBQ2xCLEtBQUs7Z0JBQU0sT0FBTztZQUNsQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJWixTQUFTO1FBQ1gscUJBQ0UsOERBQUNhO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLElBQUlaLFNBQVMsQ0FBQ0osVUFBVTtRQUN0QixxQkFDRSw4REFBQ2U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDM0IsOE9BQWdCQTt3QkFBQzJCLFdBQVU7Ozs7OztrQ0FDNUIsOERBQUNFO3dCQUFHRixXQUFVO2tDQUEyQzs7Ozs7O2tDQUN6RCw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXNCWixTQUFTOzs7Ozs7a0NBQzVDLDhEQUFDZTt3QkFDQ0MsU0FBU3JCO3dCQUNUaUIsV0FBVTs7MENBRVYsOERBQUM5QiwyT0FBYUE7Z0NBQUM4QixXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNcEQ7SUFFQSxxQkFDRSw4REFBQy9CLGlEQUFNQSxDQUFDOEIsR0FBRztRQUNUTSxTQUFTO1lBQUVDLFNBQVM7UUFBRTtRQUN0QkMsU0FBUztZQUFFRCxTQUFTO1FBQUU7UUFDdEJFLE1BQU07WUFBRUYsU0FBUztRQUFFO1FBQ25CTixXQUFVOzswQkFHViw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRztvQ0FDQ0MsU0FBU3JCO29DQUNUaUIsV0FBVTs7c0RBRVYsOERBQUM5QiwyT0FBYUE7NENBQUM4QixXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7MENBSzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFPSCxXQUFVO2tEQUNoQiw0RUFBQ3JCLDBPQUFZQTs0Q0FBQ3FCLFdBQVU7Ozs7Ozs7Ozs7O2tEQUUxQiw4REFBQ0c7d0NBQU9ILFdBQVU7a0RBQ2hCLDRFQUFDdEIsdU9BQVNBOzRDQUFDc0IsV0FBVTs7Ozs7Ozs7Ozs7a0RBRXZCLDhEQUFDRzt3Q0FBT0gsV0FBVTtrREFDaEIsNEVBQUN2QiwrT0FBaUJBOzRDQUFDdUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF2Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDL0IsaURBQU1BLENBQUM4QixHQUFHO3dCQUNUTSxTQUFTOzRCQUFFSSxHQUFHOzRCQUFJSCxTQUFTO3dCQUFFO3dCQUM3QkMsU0FBUzs0QkFBRUUsR0FBRzs0QkFBR0gsU0FBUzt3QkFBRTt3QkFDNUJJLFlBQVk7NEJBQUVDLE9BQU87d0JBQUk7d0JBQ3pCWCxXQUFVOzswQ0FFViw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNZO2dEQUFHWixXQUFVOzBEQUF5Q2hCLFNBQVM2QixLQUFLOzs7Ozs7MERBQ3JFLDhEQUFDWjtnREFBRUQsV0FBVTswREFBeUNoQixTQUFTOEIsT0FBTzs7Ozs7Ozs7Ozs7O2tEQUV4RSw4REFBQ2Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZTtnREFBS2YsV0FBVTswREFBWUgsY0FBY2IsU0FBU2MsTUFBTTs7Ozs7OzBEQUN6RCw4REFBQ2lCO2dEQUFLZixXQUFXLDhDQUFzRixPQUF4Q0wsbUJBQW1CWCxTQUFTWSxVQUFVOztvREFDbEdaLFNBQVNZLFVBQVU7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNCLDhEQUFDRztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzVCLGdQQUFrQkE7Z0RBQUM0QixXQUFVOzs7Ozs7MERBQzlCLDhEQUFDRDs7a0VBQ0MsOERBQUNFO3dEQUFFRCxXQUFVO2tFQUF3Qjs7Ozs7O2tFQUNyQyw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXFDaEIsU0FBU2dDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJckUsOERBQUNqQjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM3QiwwT0FBWUE7Z0RBQUM2QixXQUFVOzs7Ozs7MERBQ3hCLDhEQUFDRDs7a0VBQ0MsOERBQUNFO3dEQUFFRCxXQUFVO2tFQUF3Qjs7Ozs7O2tFQUNyQyw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQ1YsSUFBSWlCLEtBQUtqQyxTQUFTa0MsSUFBSSxFQUFFQyxrQkFBa0IsQ0FBQyxTQUFTOzREQUNuREMsTUFBTTs0REFDTkMsT0FBTzs0REFDUEMsS0FBSzt3REFDUDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtOLDhEQUFDdkI7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDM0IsOE9BQWdCQTtnREFBQzJCLFdBQVU7Ozs7OzswREFDNUIsOERBQUNEOztrRUFDQyw4REFBQ0U7d0RBQUVELFdBQVU7a0VBQXdCOzs7Ozs7a0VBQ3JDLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBcUNoQixTQUFTdUMsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUluRSw4REFBQ3hCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3pCLDBPQUFZQTtnREFBQ3lCLFdBQVU7Ozs7OzswREFDeEIsOERBQUNEOztrRUFDQyw4REFBQ0U7d0RBQUVELFdBQVU7a0VBQXdCOzs7Ozs7a0VBQ3JDLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBcUNoQixTQUFTYyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXZFLDhEQUFDQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzFCLHdPQUFVQTtnREFBQzBCLFdBQVU7Ozs7OzswREFDdEIsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUFvQzs7Ozs7Ozs7Ozs7O2tEQUVuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1poQixTQUFTd0MsZ0JBQWdCLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxVQUFVQyxzQkFDeEMsOERBQUNaO2dEQUVDZixXQUFVOzBEQUVUMEI7K0NBSElDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVdmLDhEQUFDMUQsaURBQU1BLENBQUM4QixHQUFHO3dCQUNUTSxTQUFTOzRCQUFFSSxHQUFHOzRCQUFJSCxTQUFTO3dCQUFFO3dCQUM3QkMsU0FBUzs0QkFBRUUsR0FBRzs0QkFBR0gsU0FBUzt3QkFBRTt3QkFDNUJJLFlBQVk7NEJBQUVDLE9BQU87d0JBQUk7d0JBQ3pCWCxXQUFVO2tDQUVWLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWmhCLFNBQVM0QyxPQUFPLGlCQUNmLDhEQUFDN0I7Z0NBQUlDLFdBQVU7MENBQXVCaEIsU0FBUzRDLE9BQU87Ozs7OzBEQUV0RCw4REFBQzdCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzNCLDhPQUFnQkE7d0NBQUMyQixXQUFVOzs7Ozs7a0RBQzVCLDhEQUFDNkI7d0NBQUc3QixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ0M7d0NBQUVELFdBQVU7OzRDQUFxQjswREFDZ0IsOERBQUM4Qjs7Ozs7MERBQ2pELDhEQUFDQztnREFBSy9CLFdBQVU7MERBQXlDaEIsU0FBU2dELFFBQVE7Ozs7Ozs7Ozs7OztrREFFNUUsOERBQUMvQjt3Q0FBRUQsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUzdDLDhEQUFDL0IsaURBQU1BLENBQUM4QixHQUFHO3dCQUNUTSxTQUFTOzRCQUFFSSxHQUFHOzRCQUFJSCxTQUFTO3dCQUFFO3dCQUM3QkMsU0FBUzs0QkFBRUUsR0FBRzs0QkFBR0gsU0FBUzt3QkFBRTt3QkFDNUJJLFlBQVk7NEJBQUVDLE9BQU87d0JBQUk7d0JBQ3pCWCxXQUFVOzswQ0FFViw4REFBQzZCO2dDQUFHN0IsV0FBVTswQ0FBMkM7Ozs7OzswQ0FDekQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3hCLDRPQUFjQTt3Q0FBQ3dCLFdBQVU7Ozs7OztrREFDMUIsOERBQUNDO3dDQUFFRCxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpDO0dBdk9NbkI7S0FBQUE7QUF5T04sK0RBQWVBLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvRG9jdW1lbnRWaWV3ZXIudHN4PzcxZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7XG4gIEFycm93TGVmdEljb24sXG4gIENhbGVuZGFySWNvbixcbiAgQnVpbGRpbmdPZmZpY2VJY29uLFxuICBEb2N1bWVudFRleHRJY29uLFxuICBUYXJnZXRJY29uLFxuICBHbG9iZUFsdEljb24sXG4gIFRyZW5kaW5nVXBJY29uLFxuICBBcnJvd0Rvd25UcmF5SWNvbixcbiAgU2hhcmVJY29uLFxuICBCb29rbWFya0ljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IFJlZ3VsYXRvcnlEb2N1bWVudCwgRG9jdW1lbnRTZXJ2aWNlIH0gZnJvbSAnLi4vc2VydmljZXMvZG9jdW1lbnRTZXJ2aWNlJztcblxuaW50ZXJmYWNlIERvY3VtZW50Vmlld2VyUHJvcHMge1xuICBkb2N1bWVudElkOiBzdHJpbmc7XG4gIG9uQmFjazogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgRG9jdW1lbnRWaWV3ZXI6IFJlYWN0LkZDPERvY3VtZW50Vmlld2VyUHJvcHM+ID0gKHsgZG9jdW1lbnRJZCwgb25CYWNrIH0pID0+IHtcbiAgY29uc3QgW2RvY3VtZW50LCBzZXREb2N1bWVudF0gPSB1c2VTdGF0ZTxSZWd1bGF0b3J5RG9jdW1lbnQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkRG9jdW1lbnQgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgICBjb25zdCBkb2MgPSBhd2FpdCBEb2N1bWVudFNlcnZpY2UuZ2V0RG9jdW1lbnQoZG9jdW1lbnRJZCk7XG4gICAgICAgIGlmIChkb2MpIHtcbiAgICAgICAgICBzZXREb2N1bWVudChkb2MpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldEVycm9yKCdEb2N1bWVudCBub3QgZm91bmQnKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHNldEVycm9yKCdFcnJvciBsb2FkaW5nIGRvY3VtZW50Jyk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgZG9jdW1lbnQ6JywgZXJyKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBsb2FkRG9jdW1lbnQoKTtcbiAgfSwgW2RvY3VtZW50SWRdKTtcblxuICBjb25zdCBnZXRDb25maWRlbmNlQ29sb3IgPSAoY29uZmlkZW5jZTogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGNvbmZpZGVuY2UgPj0gOTApIHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAnO1xuICAgIGlmIChjb25maWRlbmNlID49IDc1KSByZXR1cm4gJ3RleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctNTAnO1xuICAgIHJldHVybiAndGV4dC1yZWQtNjAwIGJnLXJlZC01MCc7XG4gIH07XG5cbiAgY29uc3QgZ2V0UmVnaW9uRmxhZyA9IChyZWdpb246IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAocmVnaW9uKSB7XG4gICAgICBjYXNlICdVUyc6IHJldHVybiAn8J+HuvCfh7gnO1xuICAgICAgY2FzZSAnQ0EnOiByZXR1cm4gJ/Cfh6jwn4emJztcbiAgICAgIGNhc2UgJ0VVJzogcmV0dXJuICfwn4eq8J+Huic7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ/CfjI0nO1xuICAgIH1cbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIGRvY3VtZW50Li4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IgfHwgIWRvY3VtZW50KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Eb2N1bWVudCBOb3QgRm91bmQ8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPntlcnJvciB8fCAnVGhlIHJlcXVlc3RlZCBkb2N1bWVudCBjb3VsZCBub3QgYmUgZm91bmQuJ308L3A+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25CYWNrfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd0xlZnRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICBCYWNrIHRvIERhc2hib2FyZFxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gICAgICBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiXG4gICAgPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHN0aWNreSB0b3AtMCB6LTEwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBoLTE2XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkJhY2t9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQmFjayB0byBEYXNoYm9hcmRcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICA8Qm9va21hcmtJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICA8U2hhcmVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICA8QXJyb3dEb3duVHJheUljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEb2N1bWVudCBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgIHsvKiBEb2N1bWVudCBIZWFkZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyB5OiAyMCwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgeTogMCwgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02IG1iLTZcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPntkb2N1bWVudC50aXRsZX08L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtbGcgbGVhZGluZy1yZWxheGVkXCI+e2RvY3VtZW50LnN1bW1hcnl9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTYgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e2dldFJlZ2lvbkZsYWcoZG9jdW1lbnQucmVnaW9uKX08L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldENvbmZpZGVuY2VDb2xvcihkb2N1bWVudC5jb25maWRlbmNlKX1gfT5cbiAgICAgICAgICAgICAgICB7ZG9jdW1lbnQuY29uZmlkZW5jZX0lIGNvbmZpZGVuY2VcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRG9jdW1lbnQgTWV0YWRhdGEgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00IHB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8QnVpbGRpbmdPZmZpY2VJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+U291cmNlPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntkb2N1bWVudC5zb3VyY2V9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8Q2FsZW5kYXJJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+RGF0ZTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtuZXcgRGF0ZShkb2N1bWVudC5kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xuICAgICAgICAgICAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICAgICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgICAgICAgICAgIGRheTogJ251bWVyaWMnXG4gICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+VHlwZTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57ZG9jdW1lbnQudHlwZX08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxHbG9iZUFsdEljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5SZWdpb248L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2RvY3VtZW50LnJlZ2lvbn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSW50ZW50IENhdGVnb3JpZXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgIDxUYXJnZXRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBmb250LW1lZGl1bVwiPkludGVudCBDYXRlZ29yaWVzPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgIHtkb2N1bWVudC5pbnRlbnRDYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctYmx1ZS01MCB0ZXh0LWJsdWUtNzAwIHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1tZFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2NhdGVnb3J5fVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBEb2N1bWVudCBDb250ZW50ICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgeTogMjAsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IHk6IDAsIG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiXG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLWdyYXkgbWF4LXctbm9uZVwiPlxuICAgICAgICAgICAge2RvY3VtZW50LmNvbnRlbnQgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcFwiPntkb2N1bWVudC5jb250ZW50fTwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktMzAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+RG9jdW1lbnQgQ29udGVudCBMb2FkaW5nPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIFRoZSBmdWxsIGRvY3VtZW50IGNvbnRlbnQgaXMgYmVpbmcgbG9hZGVkIGZyb206IDxiciAvPlxuICAgICAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwidGV4dC1zbSBiZy1ncmF5LTEwMCBweC0yIHB5LTEgcm91bmRlZFwiPntkb2N1bWVudC5maWxlUGF0aH08L2NvZGU+XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgSW4gYSBwcm9kdWN0aW9uIGVudmlyb25tZW50LCB0aGlzIHdvdWxkIGRpc3BsYXkgdGhlIGNvbXBsZXRlIHJlZ3VsYXRvcnkgZG9jdW1lbnQgY29udGVudC5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBSZWxhdGVkIERvY3VtZW50cyAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IHk6IDIwLCBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyB5OiAwLCBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4zIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiXG4gICAgICAgID5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlJlbGF0ZWQgRG9jdW1lbnRzPC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxUcmVuZGluZ1VwSWNvbiBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTMwMCBteC1hdXRvIG1iLTNcIiAvPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlJlbGF0ZWQgZG9jdW1lbnRzIHdpbGwgYmUgc2hvd24gaGVyZSBiYXNlZCBvbiBjb250ZW50IHNpbWlsYXJpdHkgYW5kIHJlZ3VsYXRvcnkgY29ubmVjdGlvbnMuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBEb2N1bWVudFZpZXdlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQXJyb3dMZWZ0SWNvbiIsIkNhbGVuZGFySWNvbiIsIkJ1aWxkaW5nT2ZmaWNlSWNvbiIsIkRvY3VtZW50VGV4dEljb24iLCJUYXJnZXRJY29uIiwiR2xvYmVBbHRJY29uIiwiVHJlbmRpbmdVcEljb24iLCJBcnJvd0Rvd25UcmF5SWNvbiIsIlNoYXJlSWNvbiIsIkJvb2ttYXJrSWNvbiIsIkRvY3VtZW50U2VydmljZSIsIkRvY3VtZW50Vmlld2VyIiwiZG9jdW1lbnRJZCIsIm9uQmFjayIsImRvY3VtZW50Iiwic2V0RG9jdW1lbnQiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJsb2FkRG9jdW1lbnQiLCJkb2MiLCJnZXREb2N1bWVudCIsImVyciIsImNvbnNvbGUiLCJnZXRDb25maWRlbmNlQ29sb3IiLCJjb25maWRlbmNlIiwiZ2V0UmVnaW9uRmxhZyIsInJlZ2lvbiIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJpbml0aWFsIiwib3BhY2l0eSIsImFuaW1hdGUiLCJleGl0IiwieSIsInRyYW5zaXRpb24iLCJkZWxheSIsImgxIiwidGl0bGUiLCJzdW1tYXJ5Iiwic3BhbiIsInNvdXJjZSIsIkRhdGUiLCJkYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwidHlwZSIsImludGVudENhdGVnb3JpZXMiLCJtYXAiLCJjYXRlZ29yeSIsImluZGV4IiwiY29udGVudCIsImgzIiwiYnIiLCJjb2RlIiwiZmlsZVBhdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/DocumentViewer.tsx\n"));

/***/ }),

/***/ "./src/components/SourceDocumentList.tsx":
/*!***********************************************!*\
  !*** ./src/components/SourceDocumentList.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,BarsArrowUpIcon,CalendarIcon,DocumentTextIcon,FunnelIcon,MagnifyingGlassIcon,MinusIcon,TrendingDownIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowLeftIcon,BarsArrowUpIcon,CalendarIcon,DocumentTextIcon,FunnelIcon,MagnifyingGlassIcon,MinusIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_services_documentService__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SourceDocumentList = (param)=>{\n    let { sourceName, onBack, onDocumentClick } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desc\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadDocuments = async ()=>{\n            try {\n                setLoading(true);\n                const filters = {\n                    source: sourceName,\n                    searchTerm: searchTerm || undefined,\n                    intentCategory: selectedCategory || undefined\n                };\n                const docs = await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.DocumentService.getDocumentsBySource(sourceName);\n                setDocuments(docs);\n            } catch (error) {\n                console.error(\"Error loading documents:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadDocuments();\n    }, [\n        sourceName,\n        searchTerm,\n        selectedCategory\n    ]);\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 90) return \"text-green-600 bg-green-50\";\n        if (confidence >= 75) return \"text-yellow-600 bg-yellow-50\";\n        return \"text-red-600 bg-red-50\";\n    };\n    const getRegionFlag = (region)=>{\n        switch(region){\n            case \"US\":\n                return \"\\uD83C\\uDDFA\\uD83C\\uDDF8\";\n            case \"CA\":\n                return \"\\uD83C\\uDDE8\\uD83C\\uDDE6\";\n            case \"EU\":\n                return \"\\uD83C\\uDDEA\\uD83C\\uDDFA\";\n            default:\n                return \"\\uD83C\\uDF0D\";\n        }\n    };\n    const getTrendIcon = (index)=>{\n        // Simulate trend based on position (newer documents trending up)\n        if (index < 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TrendingUpIcon, {\n            className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n            lineNumber: 69,\n            columnNumber: 27\n        }, undefined);\n        if (index < 4) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.MinusIcon, {\n            className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n            lineNumber: 70,\n            columnNumber: 27\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TrendingDownIcon, {\n            className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n            lineNumber: 71,\n            columnNumber: 12\n        }, undefined);\n    };\n    const sortedDocuments = [\n        ...documents\n    ].sort((a, b)=>{\n        let comparison = 0;\n        switch(sortBy){\n            case \"date\":\n                comparison = new Date(a.date).getTime() - new Date(b.date).getTime();\n                break;\n            case \"confidence\":\n                comparison = a.confidence - b.confidence;\n                break;\n            case \"title\":\n                comparison = a.title.localeCompare(b.title);\n                break;\n        }\n        return sortOrder === \"asc\" ? comparison : -comparison;\n    });\n    const uniqueCategories = [\n        ...new Set(documents.flatMap((doc)=>doc.intentCategories))\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Loading documents from \",\n                            sourceName,\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        exit: {\n            opacity: 0\n        },\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 sticky top-0 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onBack,\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowLeftIcon, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-4 text-lg font-semibold text-gray-900\",\n                                        children: [\n                                            sourceName,\n                                            \" Documents\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        documents.length,\n                                        \" documents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.MagnifyingGlassIcon, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search documents...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.FunnelIcon, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>setSelectedCategory(e.target.value),\n                                            className: \"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white min-w-[160px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                uniqueCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.BarsArrowUpIcon, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: \"\".concat(sortBy, \"-\").concat(sortOrder),\n                                            onChange: (e)=>{\n                                                const [field, order] = e.target.value.split(\"-\");\n                                                setSortBy(field);\n                                                setSortOrder(order);\n                                            },\n                                            className: \"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white min-w-[140px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date-desc\",\n                                                    children: \"Newest First\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date-asc\",\n                                                    children: \"Oldest First\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"confidence-desc\",\n                                                    children: \"High Confidence\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"confidence-asc\",\n                                                    children: \"Low Confidence\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"title-asc\",\n                                                    children: \"Title A-Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"title-desc\",\n                                                    children: \"Title Z-A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: sortedDocuments.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    y: 20,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: index * 0.05\n                                },\n                                onClick: ()=>onDocumentClick(document.id),\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-200 transition-all cursor-pointer group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                                            children: document.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        getTrendIcon(index)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 line-clamp-2\",\n                                                    children: document.summary\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap items-center gap-4 text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: new Date(document.date).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: document.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getRegionFlag(document.region)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: document.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-3\",\n                                                    children: [\n                                                        document.intentCategories.slice(0, 4).map((category, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-50 text-blue-700 text-xs font-medium rounded-md\",\n                                                                children: category\n                                                            }, idx, false, {\n                                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, undefined)),\n                                                        document.intentCategories.length > 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-gray-50 text-gray-500 text-xs font-medium rounded-md\",\n                                                            children: [\n                                                                \"+\",\n                                                                document.intentCategories.length - 4,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 flex flex-col items-end space-y-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getConfidenceColor(document.confidence)),\n                                                children: [\n                                                    document.confidence,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, document.id, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined),\n                    sortedDocuments.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_BarsArrowUpIcon_CalendarIcon_DocumentTextIcon_FunnelIcon_MagnifyingGlassIcon_MinusIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon, {\n                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No Documents Found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: searchTerm || selectedCategory ? \"Try adjusting your search or filter criteria.\" : \"No documents available from \".concat(sourceName, \" at this time.\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/FoodRegs/frontend/dashboard/src/components/SourceDocumentList.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SourceDocumentList, \"g1ZhZz2MgOxOEE9CwKrnsx30QAU=\");\n_c = SourceDocumentList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SourceDocumentList);\nvar _c;\n$RefreshReg$(_c, \"SourceDocumentList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SourceDocumentList.tsx\n"));

/***/ }),

/***/ "./src/services/documentService.ts":
/*!*****************************************!*\
  !*** ./src/services/documentService.ts ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ }),

/***/ "./src/utils/countValidation.ts":
/*!**************************************!*\
  !*** ./src/utils/countValidation.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountValidator: function() { return /* binding */ CountValidator; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_services_documentService__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Count Validation Utilities\n * \n * This module provides comprehensive validation functions to ensure\n * data integrity and count accuracy across the FoodRegs dashboard.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardSummary {\\n    dashboardSummary {\\n      totalDocuments\\n      totalSources\\n      activeSources\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetRegulationsCount {\\n    regulations {\\n      total\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL queries to match dashboard data sources\nconst GET_DASHBOARD_SUMMARY = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\nconst GET_REGULATIONS_COUNT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject1());\n/**\n * Validates dashboard summary metrics against actual data\n */ class CountValidator {\n    /**\n   * Validate total document count using real GraphQL data\n   */ static async validateTotalDocuments(displayedCount) {\n        try {\n            // Use the same GraphQL query that the dashboard uses\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n                query: GET_DASHBOARD_SUMMARY,\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            const actualCount = data.dashboardSummary.totalDocuments;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" from GraphQL but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Documents Count (GraphQL)\",\n                errorMessage: \"Error fetching GraphQL data: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate documents by source count\n   */ static async validateSourceDocumentCount(sourceName, displayedCount) {\n        try {\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourceName);\n            const actualCount = sourceDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"\".concat(sourceName, \" Document Count\"),\n                errorMessage: \"Error fetching source documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate filtered document count\n   */ static async validateFilteredDocumentCount(filters, displayedCount) {\n        try {\n            const filteredDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments(filters);\n            const actualCount = filteredDocuments.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Filtered Documents Count (\".concat(JSON.stringify(filters), \")\"),\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Filtered Documents Count\",\n                errorMessage: \"Error fetching filtered documents: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate intent category distribution counts\n   */ static async validateIntentDistribution(displayedDistribution) {\n        try {\n            const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n            const actualDistribution = this.calculateIntentDistribution(allDocuments);\n            const results = [];\n            for (const displayed of displayedDistribution){\n                const actual = actualDistribution.find((a)=>a.intent === displayed.intent);\n                const actualCount = actual ? actual.count : 0;\n                results.push({\n                    isValid: displayed.count === actualCount,\n                    expected: actualCount,\n                    actual: displayed.count,\n                    description: \"Intent Distribution - \".concat(displayed.intent),\n                    errorMessage: displayed.count !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.count) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Intent Distribution Validation\",\n                    errorMessage: \"Error calculating intent distribution: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate source performance metrics\n   */ static async validateSourcePerformance(displayedSources) {\n        try {\n            const results = [];\n            for (const displayed of displayedSources){\n                const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(displayed.source);\n                const actualCount = sourceDocuments.length;\n                results.push({\n                    isValid: displayed.documents === actualCount,\n                    expected: actualCount,\n                    actual: displayed.documents,\n                    description: \"Source Performance - \".concat(displayed.source, \" Document Count\"),\n                    errorMessage: displayed.documents !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayed.documents) : undefined\n                });\n            }\n            return results;\n        } catch (error) {\n            return [\n                {\n                    isValid: false,\n                    expected: 0,\n                    actual: 0,\n                    description: \"Source Performance Validation\",\n                    errorMessage: \"Error validating source performance: \".concat(error)\n                }\n            ];\n        }\n    }\n    /**\n   * Validate unique sources count using real GraphQL data\n   */ static async validateUniqueSourcesCount(displayedCount) {\n        try {\n            // Use the same GraphQL query that the dashboard uses\n            const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n                query: GET_DASHBOARD_SUMMARY,\n                fetchPolicy: \"network-only\" // Always get fresh data\n            });\n            const actualCount = data.dashboardSummary.totalSources;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Total Sources Count (GraphQL)\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" from GraphQL but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Total Sources Count (GraphQL)\",\n                errorMessage: \"Error fetching GraphQL data: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Validate unique intent categories count\n   */ static async validateUniqueIntentCategoriesCount(displayedCount) {\n        try {\n            const uniqueCategories = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueIntentCategories();\n            const actualCount = uniqueCategories.length;\n            return {\n                isValid: displayedCount === actualCount,\n                expected: actualCount,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: displayedCount !== actualCount ? \"Expected \".concat(actualCount, \" but displayed \").concat(displayedCount) : undefined\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                expected: 0,\n                actual: displayedCount,\n                description: \"Unique Intent Categories Count\",\n                errorMessage: \"Error fetching unique categories: \".concat(error)\n            };\n        }\n    }\n    /**\n   * Calculate actual intent distribution from documents\n   */ static calculateIntentDistribution(documents) {\n        const intentCounts = {};\n        const totalDocuments = documents.length;\n        // Count occurrences of each intent category\n        documents.forEach((doc)=>{\n            doc.intentCategories.forEach((intent)=>{\n                intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n            });\n        });\n        // Convert to distribution format\n        return Object.entries(intentCounts).map((param)=>{\n            let [intent, count] = param;\n            return {\n                intent,\n                count,\n                percentage: totalDocuments > 0 ? count / totalDocuments * 100 : 0\n            };\n        }).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Run comprehensive validation of all dashboard counts with detailed logging\n   */ static async runComprehensiveValidation(dashboardData, logger) {\n        var _dashboardData_intentDistribution, _dashboardData_sourcePerformance;\n        const results = [];\n        const log = logger || (()=>{}); // No-op if no logger provided\n        log(\"info\", \"Starting comprehensive dashboard validation\");\n        log(\"info\", \"Dashboard data structure\", {\n            summaryKeys: Object.keys(dashboardData.summary || {}),\n            intentDistributionCount: ((_dashboardData_intentDistribution = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution === void 0 ? void 0 : _dashboardData_intentDistribution.length) || 0,\n            sourcePerformanceCount: ((_dashboardData_sourcePerformance = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance === void 0 ? void 0 : _dashboardData_sourcePerformance.length) || 0\n        });\n        try {\n            var _dashboardData_intentDistribution1, _dashboardData_sourcePerformance1;\n            // Validate total documents\n            log(\"info\", \"Validating total documents count\", {\n                displayedCount: dashboardData.summary.totalDocuments\n            });\n            const totalDocsResult = await this.validateTotalDocuments(dashboardData.summary.totalDocuments);\n            results.push(totalDocsResult);\n            log(totalDocsResult.isValid ? \"success\" : \"error\", \"Total documents validation: \".concat(totalDocsResult.isValid ? \"PASSED\" : \"FAILED\"), totalDocsResult);\n            // Validate intent distribution\n            log(\"info\", \"Validating intent distribution\", {\n                intentCount: ((_dashboardData_intentDistribution1 = dashboardData.intentDistribution) === null || _dashboardData_intentDistribution1 === void 0 ? void 0 : _dashboardData_intentDistribution1.length) || 0\n            });\n            if (dashboardData.intentDistribution && dashboardData.intentDistribution.length > 0) {\n                const intentResults = await this.validateIntentDistribution(dashboardData.intentDistribution);\n                results.push(...intentResults);\n                const intentPassed = intentResults.filter((r)=>r.isValid).length;\n                log(intentPassed === intentResults.length ? \"success\" : \"warning\", \"Intent distribution validation: \".concat(intentPassed, \"/\").concat(intentResults.length, \" passed\"));\n            } else {\n                log(\"warning\", \"Intent distribution data is empty - skipping validation\");\n                results.push({\n                    isValid: false,\n                    expected: 1,\n                    actual: 0,\n                    description: \"Intent Distribution Data Availability\",\n                    errorMessage: \"No intent distribution data found in dashboard\"\n                });\n            }\n            // Validate source performance\n            log(\"info\", \"Validating source performance data\", {\n                sourceCount: ((_dashboardData_sourcePerformance1 = dashboardData.sourcePerformance) === null || _dashboardData_sourcePerformance1 === void 0 ? void 0 : _dashboardData_sourcePerformance1.length) || 0\n            });\n            if (dashboardData.sourcePerformance && dashboardData.sourcePerformance.length > 0) {\n                const sourceResults = await this.validateSourcePerformance(dashboardData.sourcePerformance);\n                results.push(...sourceResults);\n                const sourcePassed = sourceResults.filter((r)=>r.isValid).length;\n                log(sourcePassed === sourceResults.length ? \"success\" : \"warning\", \"Source performance validation: \".concat(sourcePassed, \"/\").concat(sourceResults.length, \" passed\"));\n            } else {\n                log(\"warning\", \"Source performance data is empty - skipping validation\");\n                results.push({\n                    isValid: false,\n                    expected: 1,\n                    actual: 0,\n                    description: \"Source Performance Data Availability\",\n                    errorMessage: \"No source performance data found in dashboard\"\n                });\n            }\n            // Validate unique sources count\n            log(\"info\", \"Validating unique sources count\", {\n                displayedCount: dashboardData.summary.totalSources\n            });\n            const uniqueSourcesResult = await this.validateUniqueSourcesCount(dashboardData.summary.totalSources);\n            results.push(uniqueSourcesResult);\n            log(uniqueSourcesResult.isValid ? \"success\" : \"error\", \"Unique sources validation: \".concat(uniqueSourcesResult.isValid ? \"PASSED\" : \"FAILED\"), uniqueSourcesResult);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            log(\"error\", \"Critical validation error occurred\", {\n                error: errorMessage\n            });\n            results.push({\n                isValid: false,\n                expected: 0,\n                actual: 0,\n                description: \"Comprehensive Validation\",\n                errorMessage: \"Error during validation: \".concat(errorMessage)\n            });\n        }\n        const passedTests = results.filter((r)=>r.isValid).length;\n        const failedTests = results.filter((r)=>!r.isValid).length;\n        log(\"info\", \"Validation summary calculated\", {\n            totalTests: results.length,\n            passedTests,\n            failedTests,\n            overallValid: failedTests === 0\n        });\n        const report = {\n            timestamp: new Date().toISOString(),\n            overallValid: failedTests === 0,\n            validationResults: results,\n            summary: {\n                totalTests: results.length,\n                passedTests,\n                failedTests\n            }\n        };\n        log(\"success\", \"Comprehensive validation completed\", {\n            report\n        });\n        return report;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/countValidation.ts\n"));

/***/ }),

/***/ "./src/utils/dashboardDataGenerator.ts":
/*!*********************************************!*\
  !*** ./src/utils/dashboardDataGenerator.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAccurateDashboardData: function() { return /* binding */ generateAccurateDashboardData; },\n/* harmony export */   validateDashboardData: function() { return /* binding */ validateDashboardData; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/documentService */ \"./src/services/documentService.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_services_documentService__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_apollo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/apollo */ \"./src/lib/apollo.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/**\n * Dashboard Data Generator\n * \n * Generates accurate dashboard data based on actual DocumentService data\n * to ensure data integrity and count accuracy.\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetDashboardData {\\n    dashboardData {\\n      summary {\\n        totalSources\\n        activeSources\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        healthySources\\n        failingSources\\n        overallSuccessRate\\n        totalActions\\n        pendingActions\\n      }\\n      sourceMetrics {\\n        sourceId\\n        name\\n        agency\\n        totalDocuments\\n        successRate7d\\n        lastCollectionAt\\n        lastSuccessAt\\n      }\\n      intentMetrics {\\n        intent\\n        totalDocuments\\n        documents24h\\n        documents7d\\n        avgConfidence\\n      }\\n      trendingTopics {\\n        topic\\n        mentions\\n        trend\\n        changePercent\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// GraphQL query for dashboard data\nconst GET_DASHBOARD_DATA = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_3__.gql)(_templateObject());\n/**\n * Generate accurate dashboard data from DocumentService\n */ async function generateAccurateDashboardData() {\n    try {\n        // Try to get real data from GraphQL API first\n        const { data } = await _lib_apollo__WEBPACK_IMPORTED_MODULE_2__.apolloClient.query({\n            query: GET_DASHBOARD_DATA,\n            errorPolicy: \"all\"\n        });\n        if (data === null || data === void 0 ? void 0 : data.dashboardData) {\n            var _dashboardData_intentMetrics, _dashboardData_trendingTopics, _dashboardData_sourceMetrics;\n            const dashboardData = data.dashboardData;\n            // Transform GraphQL data to our format\n            return {\n                summary: {\n                    totalDocuments: dashboardData.summary.totalDocuments,\n                    documentsToday: dashboardData.summary.documents24h,\n                    activeSources: dashboardData.summary.activeSources,\n                    totalSources: dashboardData.summary.totalSources,\n                    healthySources: dashboardData.summary.healthySources,\n                    successRate: dashboardData.summary.overallSuccessRate,\n                    recentJobs: Math.floor(dashboardData.summary.totalDocuments * 0.1),\n                    pendingActions: dashboardData.summary.pendingActions,\n                    totalActions: dashboardData.summary.totalActions\n                },\n                intentDistribution: ((_dashboardData_intentMetrics = dashboardData.intentMetrics) === null || _dashboardData_intentMetrics === void 0 ? void 0 : _dashboardData_intentMetrics.map((intent, index)=>({\n                        intent: intent.intent,\n                        count: intent.totalDocuments,\n                        percentage: dashboardData.summary.totalDocuments > 0 ? intent.totalDocuments / dashboardData.summary.totalDocuments * 100 : 0,\n                        color: [\n                            \"#3B82F6\",\n                            \"#10B981\",\n                            \"#F59E0B\",\n                            \"#EF4444\",\n                            \"#8B5CF6\",\n                            \"#06B6D4\",\n                            \"#84CC16\",\n                            \"#F97316\"\n                        ][index % 8]\n                    }))) || [],\n                trendingTopics: ((_dashboardData_trendingTopics = dashboardData.trendingTopics) === null || _dashboardData_trendingTopics === void 0 ? void 0 : _dashboardData_trendingTopics.map((topic)=>({\n                        topic: topic.topic,\n                        mentions: topic.mentions,\n                        trend: topic.trend,\n                        change: topic.changePercent\n                    }))) || [],\n                sourcePerformance: ((_dashboardData_sourceMetrics = dashboardData.sourceMetrics) === null || _dashboardData_sourceMetrics === void 0 ? void 0 : _dashboardData_sourceMetrics.map((source)=>({\n                        source: source.name,\n                        agency: source.agency,\n                        status: source.successRate7d > 90 ? \"healthy\" : source.successRate7d > 70 ? \"warning\" : \"error\",\n                        documents: source.totalDocuments,\n                        successRate: source.successRate7d,\n                        lastCollection: source.lastCollectionAt ? new Date(source.lastCollectionAt).toLocaleString() : \"Never\",\n                        primaryIntent: \"Food Safety\" // Default for now\n                    }))) || []\n            };\n        }\n        // Fallback to DocumentService if GraphQL fails\n        console.warn(\"GraphQL dashboard data not available, falling back to DocumentService\");\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const totalDocuments = allDocuments.length;\n        // Calculate documents from today (simulate some recent activity)\n        const today = new Date().toISOString().split(\"T\")[0];\n        const documentsToday = allDocuments.filter((doc)=>doc.date === today).length;\n        // Get unique sources and calculate metrics\n        const uniqueSources = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSources();\n        const totalSources = uniqueSources.length;\n        const activeSources = totalSources; // Assume all sources are active\n        const healthySources = Math.floor(totalSources * 0.8); // 80% healthy\n        // Calculate intent distribution from actual documents\n        const intentCounts = {};\n        allDocuments.forEach((doc)=>{\n            doc.intentCategories.forEach((intent)=>{\n                intentCounts[intent] = (intentCounts[intent] || 0) + 1;\n            });\n        });\n        // Convert to distribution format with colors\n        const colors = [\n            \"#3B82F6\",\n            \"#10B981\",\n            \"#F59E0B\",\n            \"#EF4444\",\n            \"#8B5CF6\",\n            \"#06B6D4\",\n            \"#84CC16\",\n            \"#F97316\"\n        ];\n        const intentDistribution = Object.entries(intentCounts).map((param, index)=>{\n            let [intent, count] = param;\n            return {\n                intent,\n                count,\n                percentage: totalDocuments > 0 ? count / totalDocuments * 100 : 0,\n                color: colors[index % colors.length]\n            };\n        }).sort((a, b)=>b.count - a.count);\n        // Generate source performance data from actual sources\n        const sourcePerformance = await Promise.all(uniqueSources.map(async (source)=>{\n            var _Object_entries_sort_;\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(source);\n            const documentCount = sourceDocuments.length;\n            // Determine primary intent for this source\n            const sourceIntentCounts = {};\n            sourceDocuments.forEach((doc)=>{\n                doc.intentCategories.forEach((intent)=>{\n                    sourceIntentCounts[intent] = (sourceIntentCounts[intent] || 0) + 1;\n                });\n            });\n            const primaryIntent = ((_Object_entries_sort_ = Object.entries(sourceIntentCounts).sort((param, param1)=>{\n                let [, a] = param, [, b] = param1;\n                return b - a;\n            })[0]) === null || _Object_entries_sort_ === void 0 ? void 0 : _Object_entries_sort_[0]) || \"General\";\n            // Simulate status based on document count and recency\n            let status = \"healthy\";\n            if (documentCount === 0) {\n                status = \"error\";\n            } else if (documentCount === 1) {\n                status = \"warning\";\n            }\n            // Simulate success rate based on status\n            let successRate = 95;\n            if (status === \"warning\") successRate = 85;\n            if (status === \"error\") successRate = 60;\n            // Simulate last collection time\n            const lastCollectionTimes = [\n                \"30 min ago\",\n                \"1 hour ago\",\n                \"2 hours ago\",\n                \"4 hours ago\",\n                \"1 day ago\"\n            ];\n            const lastCollection = lastCollectionTimes[Math.floor(Math.random() * lastCollectionTimes.length)];\n            return {\n                source,\n                status,\n                documents: documentCount,\n                successRate,\n                lastCollection,\n                primaryIntent\n            };\n        }));\n        // Generate trending topics from intent categories\n        const trendingTopics = Object.entries(intentCounts).slice(0, 8).map((param, index)=>{\n            let [topic, count] = param;\n            return {\n                topic,\n                mentions: count,\n                trend: index < 3 ? \"up\" : index < 6 ? \"stable\" : \"down\",\n                change: index < 3 ? Math.floor(Math.random() * 15) + 5 : index < 6 ? 0 : -(Math.floor(Math.random() * 10) + 1)\n            };\n        });\n        return {\n            summary: {\n                totalDocuments,\n                documentsToday: documentsToday || Math.floor(totalDocuments * 0.1),\n                activeSources,\n                totalSources,\n                healthySources,\n                successRate: 94.2,\n                recentJobs: Math.floor(totalDocuments * 0.5),\n                pendingActions: Math.floor(totalDocuments * 0.3),\n                totalActions: Math.floor(totalDocuments * 0.8) // Simulate total actions\n            },\n            intentDistribution,\n            trendingTopics,\n            sourcePerformance: sourcePerformance.sort((a, b)=>b.documents - a.documents)\n        };\n    } catch (error) {\n        console.error(\"Error generating dashboard data:\", error);\n        // Return minimal fallback data\n        return {\n            summary: {\n                totalDocuments: 0,\n                documentsToday: 0,\n                activeSources: 0,\n                totalSources: 0,\n                healthySources: 0,\n                successRate: 0,\n                recentJobs: 0,\n                pendingActions: 0,\n                totalActions: 0\n            },\n            intentDistribution: [],\n            trendingTopics: [],\n            sourcePerformance: []\n        };\n    }\n}\n/**\n * Validate dashboard data against DocumentService\n */ async function validateDashboardData(dashboardData) {\n    const errors = [];\n    try {\n        const allDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocuments();\n        const actualTotalDocuments = allDocuments.length;\n        // Validate total documents\n        if (dashboardData.summary.totalDocuments !== actualTotalDocuments) {\n            errors.push(\"Total documents mismatch: expected \".concat(actualTotalDocuments, \", got \").concat(dashboardData.summary.totalDocuments));\n        }\n        // Validate source count\n        const actualSources = _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getUniqueSources();\n        if (dashboardData.summary.totalSources !== actualSources.length) {\n            errors.push(\"Total sources mismatch: expected \".concat(actualSources.length, \", got \").concat(dashboardData.summary.totalSources));\n        }\n        // Validate source performance data\n        for (const sourcePerf of dashboardData.sourcePerformance){\n            const sourceDocuments = await _services_documentService__WEBPACK_IMPORTED_MODULE_1__.DocumentService.getDocumentsBySource(sourcePerf.source);\n            if (sourcePerf.documents !== sourceDocuments.length) {\n                errors.push(\"Source \".concat(sourcePerf.source, \" document count mismatch: expected \").concat(sourceDocuments.length, \", got \").concat(sourcePerf.documents));\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    } catch (error) {\n        errors.push(\"Validation error: \".concat(error));\n        return {\n            isValid: false,\n            errors\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/dashboardDataGenerator.ts\n"));

/***/ })

});