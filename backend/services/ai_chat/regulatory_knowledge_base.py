"""
Regulatory Knowledge Base for AI Chat System
Provides comprehensive regulatory context for AI responses
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload

from database.models import Regulation as DBRegulation, RegulatorySource as DBRegulatorySource
from database.connection import get_async_session
from services.regulatory_ingestion.base_collector import RegionType, DocumentType
from services.regulatory_ingestion.content_processor import ContentProcessor

logger = logging.getLogger(__name__)


class RegulatoryKnowledgeBase:
    """Provides comprehensive regulatory knowledge for AI chat responses"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
        # Context templates for different query types
        self.context_templates = {
            'current_regulation': """
CURRENT REGULATION CONTEXT:
Title: {title}
Region: {region}
Type: {doc_type}
Published: {pub_date}
Source: {source}

Summary: {summary}

Key Sections:
{key_sections}

Compliance Requirements:
{compliance_requirements}
""",
            
            'regulatory_comparison': """
REGULATORY COMPARISON CONTEXT:
Comparing regulations across {regions}

{regulation_summaries}

Key Differences:
{differences}

Common Requirements:
{common_requirements}
""",
            
            'recent_updates': """
RECENT REGULATORY UPDATES:
Time Period: {time_period}
Total Updates: {update_count}

{update_summaries}

Impact Analysis:
{impact_analysis}
""",
            
            'compliance_guidance': """
COMPLIANCE GUIDANCE CONTEXT:
Topic: {topic}
Applicable Regions: {regions}

Relevant Regulations:
{relevant_regulations}

Key Requirements:
{key_requirements}

Recommended Actions:
{recommended_actions}
"""
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = await get_async_session().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_regulatory_context(self, query: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get comprehensive regulatory context for a user query"""
        try:
            # Analyze the query to determine intent and extract key terms
            query_analysis = await self._analyze_query(query)
            
            # Get relevant regulations based on query analysis
            relevant_regulations = await self._find_relevant_regulations(
                query_analysis['keywords'],
                query_analysis['regions'],
                query_analysis['topics'],
                user_context
            )
            
            # Build context based on query type
            context = await self._build_context(query_analysis, relevant_regulations, user_context)
            
            return {
                'context': context,
                'relevant_regulations': relevant_regulations,
                'query_analysis': query_analysis,
                'confidence': self._calculate_confidence(query_analysis, relevant_regulations)
            }
            
        except Exception as e:
            logger.error(f"Error getting regulatory context: {str(e)}")
            return {
                'context': "I apologize, but I'm having trouble accessing the regulatory database right now.",
                'relevant_regulations': [],
                'query_analysis': {},
                'confidence': 0.0
            }
    
    async def _analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze user query to extract intent, keywords, and context"""
        query_lower = query.lower()
        
        # Extract regions mentioned
        regions = []
        region_keywords = {
            'us': ['usa', 'united states', 'america', 'fda', 'usda', 'cfr'],
            'eu': ['europe', 'european union', 'efsa', 'eur-lex'],
            'uk': ['united kingdom', 'britain', 'british', 'uk legislation'],
            'ca': ['canada', 'canadian', 'cfia', 'health canada']
        }
        
        for region, keywords in region_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                regions.append(region.upper())
        
        # Extract food safety topics
        topics = []
        topic_keywords = {
            'food_safety': ['food safety', 'haccp', 'pathogen', 'contamination', 'foodborne'],
            'labeling': ['label', 'nutrition facts', 'ingredient', 'allergen', 'claim'],
            'additives': ['additive', 'preservative', 'chemical', 'artificial'],
            'organic': ['organic', 'natural', 'certified'],
            'import_export': ['import', 'export', 'trade', 'border'],
            'manufacturing': ['manufacturing', 'processing', 'facility', 'gmp']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                topics.append(topic)
        
        # Determine query intent
        intent = 'general'
        if any(word in query_lower for word in ['current', 'latest', 'new', 'recent']):
            intent = 'current_status'
        elif any(word in query_lower for word in ['compare', 'difference', 'versus', 'vs']):
            intent = 'comparison'
        elif any(word in query_lower for word in ['comply', 'compliance', 'requirement', 'must']):
            intent = 'compliance'
        elif any(word in query_lower for word in ['update', 'change', 'amendment', 'revision']):
            intent = 'updates'
        
        # Extract key terms (simplified keyword extraction)
        import re
        words = re.findall(r'\b\w{3,}\b', query_lower)
        keywords = [word for word in words if word not in [
            'the', 'and', 'for', 'are', 'what', 'how', 'can', 'you', 'tell', 'about'
        ]]
        
        return {
            'intent': intent,
            'keywords': keywords[:10],  # Limit to top 10
            'regions': regions,
            'topics': topics,
            'original_query': query
        }
    
    async def _find_relevant_regulations(self, keywords: List[str], regions: List[str], 
                                       topics: List[str], user_context: Dict = None) -> List[DBRegulation]:
        """Find regulations relevant to the query"""
        try:
            # Build search query
            search_conditions = [DBRegulation.is_latest_version == True]
            
            # Add keyword search
            if keywords:
                keyword_conditions = []
                for keyword in keywords:
                    keyword_conditions.extend([
                        DBRegulation.title.ilike(f'%{keyword}%'),
                        DBRegulation.summary.ilike(f'%{keyword}%'),
                        DBRegulation.full_text.ilike(f'%{keyword}%')
                    ])
                search_conditions.append(or_(*keyword_conditions))
            
            # Add region filter
            if regions:
                search_conditions.append(DBRegulation.region.in_(regions))
            
            # Add topic filter (simplified - would use tags in real implementation)
            if topics:
                topic_conditions = []
                for topic in topics:
                    topic_conditions.append(DBRegulation.full_text.ilike(f'%{topic}%'))
                search_conditions.append(or_(*topic_conditions))
            
            # Execute search
            query = select(DBRegulation).where(and_(*search_conditions)).order_by(
                DBRegulation.pub_date.desc()
            ).limit(20)
            
            result = await self.session.execute(query)
            regulations = result.scalars().all()
            
            # Score and rank results
            scored_regulations = []
            for reg in regulations:
                score = self._calculate_relevance_score(reg, keywords, topics)
                scored_regulations.append((reg, score))
            
            # Sort by score and return top results
            scored_regulations.sort(key=lambda x: x[1], reverse=True)
            return [reg for reg, score in scored_regulations[:10]]
            
        except Exception as e:
            logger.error(f"Error finding relevant regulations: {str(e)}")
            return []
    
    def _calculate_relevance_score(self, regulation: DBRegulation, keywords: List[str], topics: List[str]) -> float:
        """Calculate relevance score for a regulation"""
        score = 0.0
        
        try:
            # Score based on keyword matches in title (higher weight)
            title_lower = regulation.title.lower()
            for keyword in keywords:
                if keyword in title_lower:
                    score += 3.0
            
            # Score based on keyword matches in summary
            if regulation.summary:
                summary_lower = regulation.summary.lower()
                for keyword in keywords:
                    if keyword in summary_lower:
                        score += 2.0
            
            # Score based on recency (more recent = higher score)
            if regulation.pub_date:
                days_old = (datetime.now() - regulation.pub_date).days
                recency_score = max(0, 1.0 - (days_old / 365))  # Decay over a year
                score += recency_score
            
            # Score based on document type (regulations > notices)
            if regulation.doc_type == 'Regulation':
                score += 1.0
            elif regulation.doc_type == 'Act':
                score += 0.8
            
        except Exception as e:
            logger.error(f"Error calculating relevance score: {str(e)}")
        
        return score
    
    async def _build_context(self, query_analysis: Dict, regulations: List[DBRegulation], 
                           user_context: Dict = None) -> str:
        """Build comprehensive context for AI response"""
        try:
            intent = query_analysis['intent']
            
            if intent == 'current_status' and regulations:
                return await self._build_current_regulation_context(regulations[0])
            
            elif intent == 'comparison' and len(regulations) > 1:
                return await self._build_comparison_context(regulations)
            
            elif intent == 'compliance':
                return await self._build_compliance_context(regulations, query_analysis['topics'])
            
            elif intent == 'updates':
                return await self._build_updates_context(regulations)
            
            else:
                return await self._build_general_context(regulations, query_analysis)
                
        except Exception as e:
            logger.error(f"Error building context: {str(e)}")
            return "I found some relevant regulations but had trouble processing the details."
    
    async def _build_current_regulation_context(self, regulation: DBRegulation) -> str:
        """Build context for current regulation queries"""
        try:
            # Extract key sections (simplified)
            key_sections = self._extract_key_sections(regulation.full_text)
            
            # Extract compliance requirements (simplified)
            compliance_requirements = self._extract_compliance_requirements(regulation.full_text)
            
            return self.context_templates['current_regulation'].format(
                title=regulation.title,
                region=regulation.region,
                doc_type=regulation.doc_type,
                pub_date=regulation.pub_date.strftime('%Y-%m-%d') if regulation.pub_date else 'Unknown',
                source=regulation.source_id,
                summary=regulation.summary or 'No summary available',
                key_sections='\n'.join([f"- {section}" for section in key_sections[:5]]),
                compliance_requirements='\n'.join([f"- {req}" for req in compliance_requirements[:5]])
            )
            
        except Exception as e:
            logger.error(f"Error building current regulation context: {str(e)}")
            return f"Current regulation: {regulation.title}"
    
    async def _build_comparison_context(self, regulations: List[DBRegulation]) -> str:
        """Build context for regulatory comparison queries"""
        try:
            regions = list(set([reg.region for reg in regulations]))
            
            regulation_summaries = []
            for reg in regulations[:3]:  # Limit to 3 for comparison
                summary = f"{reg.region} - {reg.title}: {reg.summary[:200]}..." if reg.summary else f"{reg.region} - {reg.title}"
                regulation_summaries.append(summary)
            
            return self.context_templates['regulatory_comparison'].format(
                regions=', '.join(regions),
                regulation_summaries='\n\n'.join(regulation_summaries),
                differences="Analysis of differences would be provided here",
                common_requirements="Common requirements would be identified here"
            )
            
        except Exception as e:
            logger.error(f"Error building comparison context: {str(e)}")
            return "Comparison of multiple regulations"
    
    async def _build_compliance_context(self, regulations: List[DBRegulation], topics: List[str]) -> str:
        """Build context for compliance queries"""
        try:
            regions = list(set([reg.region for reg in regulations]))
            topic_str = ', '.join(topics) if topics else 'General compliance'
            
            relevant_regulations = []
            for reg in regulations[:5]:
                relevant_regulations.append(f"- {reg.title} ({reg.region})")
            
            return self.context_templates['compliance_guidance'].format(
                topic=topic_str,
                regions=', '.join(regions),
                relevant_regulations='\n'.join(relevant_regulations),
                key_requirements="Key requirements would be extracted here",
                recommended_actions="Recommended compliance actions would be provided here"
            )
            
        except Exception as e:
            logger.error(f"Error building compliance context: {str(e)}")
            return "Compliance guidance context"
    
    async def _build_updates_context(self, regulations: List[DBRegulation]) -> str:
        """Build context for regulatory updates queries"""
        try:
            recent_regs = [reg for reg in regulations if reg.pub_date and 
                          (datetime.now() - reg.pub_date).days <= 90]
            
            update_summaries = []
            for reg in recent_regs[:5]:
                days_ago = (datetime.now() - reg.pub_date).days if reg.pub_date else 0
                summary = f"- {reg.title} ({reg.region}) - {days_ago} days ago"
                update_summaries.append(summary)
            
            return self.context_templates['recent_updates'].format(
                time_period="Last 90 days",
                update_count=len(recent_regs),
                update_summaries='\n'.join(update_summaries),
                impact_analysis="Impact analysis would be provided here"
            )
            
        except Exception as e:
            logger.error(f"Error building updates context: {str(e)}")
            return "Recent regulatory updates context"
    
    async def _build_general_context(self, regulations: List[DBRegulation], query_analysis: Dict) -> str:
        """Build general context for other query types"""
        try:
            if not regulations:
                return "I couldn't find specific regulations matching your query. Could you provide more details about the specific topic or region you're interested in?"
            
            context_parts = [
                f"I found {len(regulations)} relevant regulations for your query.",
                "",
                "Most relevant regulations:"
            ]
            
            for i, reg in enumerate(regulations[:5], 1):
                context_parts.append(f"{i}. {reg.title} ({reg.region})")
                if reg.summary:
                    context_parts.append(f"   Summary: {reg.summary[:150]}...")
                context_parts.append("")
            
            return '\n'.join(context_parts)
            
        except Exception as e:
            logger.error(f"Error building general context: {str(e)}")
            return "General regulatory context"
    
    def _extract_key_sections(self, full_text: str) -> List[str]:
        """Extract key sections from regulation text"""
        # Simplified implementation - would use more sophisticated NLP
        sections = []
        try:
            import re
            
            # Look for section headers
            section_patterns = [
                r'Section\s+\d+[:\-]?\s*([^\n]+)',
                r'Article\s+\d+[:\-]?\s*([^\n]+)',
                r'§\s*\d+\.\d+\s+([^\n]+)'
            ]
            
            for pattern in section_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                sections.extend(matches[:3])  # Limit to 3 per pattern
            
        except Exception as e:
            logger.error(f"Error extracting key sections: {str(e)}")
        
        return sections[:5]  # Return top 5 sections
    
    def _extract_compliance_requirements(self, full_text: str) -> List[str]:
        """Extract compliance requirements from regulation text"""
        requirements = []
        try:
            import re
            
            # Look for compliance language
            requirement_patterns = [
                r'shall\s+([^.]+)',
                r'must\s+([^.]+)',
                r'required\s+to\s+([^.]+)'
            ]
            
            for pattern in requirement_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                requirements.extend([match.strip() for match in matches[:2]])
            
        except Exception as e:
            logger.error(f"Error extracting compliance requirements: {str(e)}")
        
        return requirements[:5]  # Return top 5 requirements
    
    def _calculate_confidence(self, query_analysis: Dict, regulations: List[DBRegulation]) -> float:
        """Calculate confidence score for the response"""
        try:
            confidence = 0.5  # Base confidence
            
            # Increase confidence based on number of relevant regulations found
            if regulations:
                confidence += min(0.3, len(regulations) * 0.05)
            
            # Increase confidence if specific regions/topics were identified
            if query_analysis.get('regions'):
                confidence += 0.1
            
            if query_analysis.get('topics'):
                confidence += 0.1
            
            # Cap at 1.0
            return min(1.0, confidence)
            
        except Exception:
            return 0.5
    
    async def get_recent_updates(self, days: int = 30, region: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent regulatory updates"""
        try:
            since_date = datetime.now() - timedelta(days=days)
            
            query = select(DBRegulation).where(
                and_(
                    DBRegulation.is_latest_version == True,
                    DBRegulation.pub_date >= since_date
                )
            )
            
            if region:
                query = query.where(DBRegulation.region == region.upper())
            
            query = query.order_by(DBRegulation.pub_date.desc()).limit(20)
            
            result = await self.session.execute(query)
            regulations = result.scalars().all()
            
            updates = []
            for reg in regulations:
                updates.append({
                    'id': reg.id,
                    'title': reg.title,
                    'region': reg.region,
                    'doc_type': reg.doc_type,
                    'pub_date': reg.pub_date.isoformat() if reg.pub_date else None,
                    'summary': reg.summary,
                    'url': reg.url
                })
            
            return updates
            
        except Exception as e:
            logger.error(f"Error getting recent updates: {str(e)}")
            return []


# Convenience functions for AI chat integration
async def get_regulatory_context_for_chat(query: str, user_context: Dict[str, Any] = None) -> str:
    """Get regulatory context formatted for AI chat responses"""
    async with RegulatoryKnowledgeBase() as kb:
        result = await kb.get_regulatory_context(query, user_context)
        return result['context']


async def search_regulations_for_chat(query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Search regulations and return simplified results for chat"""
    async with RegulatoryKnowledgeBase() as kb:
        result = await kb.get_regulatory_context(query)

        simplified_results = []
        for reg in result['relevant_regulations'][:limit]:
            simplified_results.append({
                'title': reg.title,
                'region': reg.region,
                'type': reg.doc_type,
                'summary': reg.summary[:200] + '...' if reg.summary and len(reg.summary) > 200 else reg.summary,
                'url': reg.url,
                'published': reg.pub_date.strftime('%Y-%m-%d') if reg.pub_date else None
            })

        return simplified_results
