"""
Base Regulatory Data Collector
Provides common functionality for all regulatory source collectors
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
import hashlib
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class RegionType(Enum):
    US = "US"
    EU = "EU" 
    UK = "UK"
    CA = "CA"


class DocumentType(Enum):
    REGULATION = "Regulation"
    DIRECTIVE = "Directive"
    GUIDANCE = "Guidance"
    NOTICE = "Notice"
    OPINION = "Opinion"
    ACT = "Act"


@dataclass
class RegulatoryDocument:
    """Standardized regulatory document structure"""
    source_id: str
    title: str
    document_id: str  # Official document identifier (e.g., CFR section, CELEX number)
    url: str
    content: str
    summary: Optional[str]
    region: RegionType
    doc_type: DocumentType
    effective_date: Optional[datetime]
    published_date: datetime
    last_modified: Optional[datetime]
    version: str
    language: str = "en"
    
    # Cross-references and relationships
    amends: List[str] = None  # Documents this amends
    amended_by: List[str] = None  # Documents that amend this
    cites: List[str] = None  # Documents this cites
    cited_by: List[str] = None  # Documents that cite this
    
    # Content metadata
    sections: Dict[str, str] = None  # Section ID -> content mapping
    keywords: List[str] = None
    topics: List[str] = None
    
    # Change tracking
    content_hash: str = None
    previous_version_hash: Optional[str] = None
    change_summary: Optional[str] = None
    
    def __post_init__(self):
        if self.content_hash is None:
            self.content_hash = self._calculate_hash()
        if self.amends is None:
            self.amends = []
        if self.amended_by is None:
            self.amended_by = []
        if self.cites is None:
            self.cites = []
        if self.cited_by is None:
            self.cited_by = []
        if self.sections is None:
            self.sections = {}
        if self.keywords is None:
            self.keywords = []
        if self.topics is None:
            self.topics = []
    
    def _calculate_hash(self) -> str:
        """Calculate content hash for change detection"""
        content_for_hash = f"{self.title}|{self.content}|{self.effective_date}"
        return hashlib.sha256(content_for_hash.encode()).hexdigest()


@dataclass
class CollectionResult:
    """Result of a collection operation"""
    success: bool
    documents_collected: int
    documents_updated: int
    documents_new: int
    errors: List[str]
    duration_seconds: float
    last_collection_time: datetime
    next_collection_time: Optional[datetime]


class BaseRegulatoryCollector(ABC):
    """Base class for all regulatory data collectors"""
    
    def __init__(self, source_name: str, region: RegionType, base_url: str, 
                 rate_limit_per_minute: int = 60, timeout_seconds: int = 30):
        self.source_name = source_name
        self.region = region
        self.base_url = base_url
        self.rate_limit_per_minute = rate_limit_per_minute
        self.timeout_seconds = timeout_seconds
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_request_time = 0
        self.request_count = 0
        self.request_window_start = datetime.now()
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout_seconds),
            headers={'User-Agent': 'FoodRegs-Platform/1.0 (Regulatory Intelligence)'}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _rate_limit(self):
        """Enforce rate limiting"""
        now = datetime.now()
        
        # Reset counter if window has passed
        if (now - self.request_window_start).total_seconds() >= 60:
            self.request_count = 0
            self.request_window_start = now
        
        # Check if we've hit the rate limit
        if self.request_count >= self.rate_limit_per_minute:
            sleep_time = 60 - (now - self.request_window_start).total_seconds()
            if sleep_time > 0:
                logger.info(f"Rate limit reached for {self.source_name}, sleeping {sleep_time:.1f}s")
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.request_window_start = datetime.now()
        
        self.request_count += 1
    
    async def _make_request(self, url: str, params: Optional[Dict] = None, 
                          headers: Optional[Dict] = None) -> Tuple[bool, Optional[str], Optional[Dict]]:
        """Make HTTP request with rate limiting and error handling"""
        await self._rate_limit()
        
        try:
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    if 'application/json' in content_type:
                        data = await response.json()
                        return True, None, data
                    else:
                        text = await response.text()
                        return True, text, None
                else:
                    error_msg = f"HTTP {response.status} for {url}"
                    logger.error(error_msg)
                    return False, error_msg, None
                    
        except asyncio.TimeoutError:
            error_msg = f"Timeout requesting {url}"
            logger.error(error_msg)
            return False, error_msg, None
        except Exception as e:
            error_msg = f"Error requesting {url}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg, None
    
    @abstractmethod
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect current regulations from the source"""
        pass
    
    @abstractmethod
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect changes since the given date"""
        pass
    
    @abstractmethod
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get a specific document by its official ID"""
        pass
    
    def _extract_sections(self, content: str, doc_type: DocumentType) -> Dict[str, str]:
        """Extract sections from document content (to be overridden by subclasses)"""
        # Default implementation - just return the full content
        return {"full_text": content}
    
    def _extract_cross_references(self, content: str) -> Tuple[List[str], List[str]]:
        """Extract citations and amendments from content (to be overridden by subclasses)"""
        # Default implementation - return empty lists
        return [], []
    
    def _normalize_content(self, raw_content: str) -> str:
        """Normalize content format (remove extra whitespace, etc.)"""
        import re
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', raw_content.strip())
        # Remove HTML tags if present
        content = re.sub(r'<[^>]+>', '', content)
        return content
    
    def _generate_summary(self, content: str, max_length: int = 500) -> str:
        """Generate a summary of the document content"""
        # Simple implementation - take first sentences up to max_length
        sentences = content.split('. ')
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) <= max_length:
                summary += sentence + ". "
            else:
                break
        return summary.strip()
