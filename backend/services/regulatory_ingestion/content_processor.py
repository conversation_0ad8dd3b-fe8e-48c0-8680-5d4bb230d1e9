"""
Regulatory Content Processing Pipeline
Handles normalization, storage, indexing, and cross-referencing of regulatory documents
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
import hashlib
import re
from dataclasses import asdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload

from database.models import (
    Regulation as DBRegulation,
    RegulatorySource as DBRegulatorySource,
    Action as DBAction
)
from database.connection import get_async_session
from .base_collector import RegulatoryDocument, RegionType, DocumentType

logger = logging.getLogger(__name__)


class ContentProcessor:
    """Processes and stores regulatory documents with full-text indexing and cross-referencing"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
        # Cross-reference patterns for different regions
        self.cross_ref_patterns = {
            RegionType.US: [
                r'(\d+)\s+CFR\s+(\d+(?:\.\d+)?)',  # CFR references
                r'(\d+)\s+U\.?S\.?C\.?\s+(\d+)',   # USC references
                r'Federal Register\s+(\d+)\s+FR\s+(\d+)'  # Federal Register
            ],
            RegionType.EU: [
                r'Regulation\s+\(EU\)\s+(\d+/\d+)',  # EU Regulations
                r'Directive\s+(\d+/\d+/EU)',          # EU Directives
                r'CELEX:(\d+[A-Z]\d+)'                # CELEX numbers
            ],
            RegionType.UK: [
                r'S\.?I\.?\s+(\d+/\d+)',              # Statutory Instruments
                r'(\d+)\s+c\.\s+(\d+)',               # Acts of Parliament
                r'EUR\s+(\d+/\d+)'                    # Retained EU law
            ],
            RegionType.CA: [
                r'([A-Z]-\d+(?:\.\d+)?)',             # Canadian Acts
                r'SOR/(\d+-\d+)',                     # Statutory Orders
                r'C\.R\.C\.?,?\s+c\.\s+(\d+)'         # Consolidated Regulations
            ]
        }
        
        # Food safety keywords for enhanced classification
        self.food_keywords = {
            'food_safety': [
                'HACCP', 'hazard analysis', 'critical control point', 'pathogen',
                'contamination', 'foodborne illness', 'food safety', 'sanitation',
                'hygiene', 'microbiological', 'salmonella', 'listeria', 'e.coli'
            ],
            'labeling': [
                'nutrition facts', 'ingredient list', 'allergen', 'label',
                'nutritional information', 'health claim', 'nutrient content',
                'front-of-package', 'bilingual labeling'
            ],
            'additives_chemicals': [
                'food additive', 'preservative', 'artificial', 'natural flavor',
                'color additive', 'sweetener', 'pesticide residue', 'chemical',
                'maximum residue limit', 'tolerance level'
            ],
            'organic_natural': [
                'organic', 'natural', 'certified organic', 'organic standard',
                'organic certification', 'organic production', 'organic processing'
            ],
            'trade_import': [
                'import', 'export', 'international trade', 'border inspection',
                'customs', 'foreign supplier', 'certificate of origin',
                'phytosanitary', 'equivalence agreement'
            ],
            'manufacturing': [
                'good manufacturing practice', 'GMP', 'facility registration',
                'processing', 'manufacturing', 'equipment', 'cleaning',
                'validation', 'verification'
            ]
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = await get_async_session().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.__aexit__(exc_type, exc_val, exc_tb)
    
    async def process_documents(self, documents: List[RegulatoryDocument]) -> Dict[str, Any]:
        """Process a batch of regulatory documents"""
        start_time = datetime.now()
        results = {
            'processed': 0,
            'new': 0,
            'updated': 0,
            'errors': [],
            'cross_references_created': 0
        }
        
        try:
            for doc in documents:
                try:
                    # Check if document already exists
                    existing_doc = await self._get_existing_document(doc.source_id, doc.document_id)
                    
                    if existing_doc:
                        # Check if content has changed
                        if existing_doc.hash != doc.content_hash:
                            await self._update_document(existing_doc, doc)
                            results['updated'] += 1
                        # else: no changes, skip
                    else:
                        # Create new document
                        await self._create_document(doc)
                        results['new'] += 1
                    
                    results['processed'] += 1
                    
                except Exception as e:
                    error_msg = f"Error processing document {doc.document_id}: {str(e)}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            # Process cross-references for all documents
            cross_refs = await self._process_cross_references(documents)
            results['cross_references_created'] = cross_refs
            
            # Commit all changes
            await self.session.commit()
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Processed {results['processed']} documents in {duration:.2f}s")
            
        except Exception as e:
            await self.session.rollback()
            error_msg = f"Error in batch processing: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    async def _get_existing_document(self, source_id: str, document_id: str) -> Optional[DBRegulation]:
        """Get existing document from database"""
        try:
            query = select(DBRegulation).where(
                and_(
                    DBRegulation.source_id == source_id,
                    DBRegulation.record_id == document_id
                )
            )
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting existing document: {str(e)}")
            return None
    
    async def _create_document(self, doc: RegulatoryDocument) -> DBRegulation:
        """Create new document in database"""
        try:
            # Get or create source
            source = await self._get_or_create_source(doc.source_id, doc.region)
            
            # Enhanced content processing
            processed_content = await self._enhance_content(doc)
            
            # Create regulation record
            db_doc = DBRegulation(
                record_id=doc.document_id,
                version=1,
                source_id=source.id,
                url=doc.url,
                title=doc.title,
                pub_date=doc.published_date,
                ingested_at=datetime.now(),
                region=doc.region.value,
                doc_type=doc.doc_type.value,
                language=doc.language,
                summary=doc.summary,
                full_text=processed_content['enhanced_content'],
                raw_uri=doc.url,
                hash=doc.content_hash,
                tags=self._create_tags_json(doc),
                is_latest_version=True,
                word_count=len(processed_content['enhanced_content'].split()),
                text_length=len(processed_content['enhanced_content']),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.session.add(db_doc)
            await self.session.flush()  # Get the ID
            
            # Create actions if any were identified
            if processed_content['suggested_actions']:
                await self._create_actions(db_doc.id, processed_content['suggested_actions'])
            
            return db_doc
            
        except Exception as e:
            logger.error(f"Error creating document: {str(e)}")
            raise
    
    async def _update_document(self, existing_doc: DBRegulation, new_doc: RegulatoryDocument):
        """Update existing document with new version"""
        try:
            # Mark current version as not latest
            existing_doc.is_latest_version = False
            
            # Enhanced content processing
            processed_content = await self._enhance_content(new_doc)
            
            # Create new version
            new_version = DBRegulation(
                record_id=new_doc.document_id,
                version=existing_doc.version + 1,
                source_id=existing_doc.source_id,
                url=new_doc.url,
                title=new_doc.title,
                pub_date=new_doc.published_date,
                ingested_at=datetime.now(),
                region=new_doc.region.value,
                doc_type=new_doc.doc_type.value,
                language=new_doc.language,
                summary=new_doc.summary,
                full_text=processed_content['enhanced_content'],
                raw_uri=new_doc.url,
                hash=new_doc.content_hash,
                tags=self._create_tags_json(new_doc),
                is_latest_version=True,
                previous_version_id=existing_doc.id,
                word_count=len(processed_content['enhanced_content'].split()),
                text_length=len(processed_content['enhanced_content']),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.session.add(new_version)
            await self.session.flush()
            
            # Create actions for new version
            if processed_content['suggested_actions']:
                await self._create_actions(new_version.id, processed_content['suggested_actions'])
            
        except Exception as e:
            logger.error(f"Error updating document: {str(e)}")
            raise
    
    async def _enhance_content(self, doc: RegulatoryDocument) -> Dict[str, Any]:
        """Enhance document content with additional processing"""
        try:
            enhanced_content = doc.content
            suggested_actions = []
            
            # Extract and enhance sections
            sections = self._extract_enhanced_sections(doc.content, doc.region)
            
            # Identify compliance requirements
            compliance_requirements = self._identify_compliance_requirements(doc.content, doc.region)
            
            # Generate suggested actions based on content
            if compliance_requirements:
                suggested_actions = self._generate_compliance_actions(
                    compliance_requirements, doc.title, doc.region
                )
            
            # Add structured metadata to content
            metadata_section = self._create_metadata_section(doc, sections, compliance_requirements)
            enhanced_content = f"{enhanced_content}\n\n{metadata_section}"
            
            return {
                'enhanced_content': enhanced_content,
                'sections': sections,
                'compliance_requirements': compliance_requirements,
                'suggested_actions': suggested_actions
            }
            
        except Exception as e:
            logger.error(f"Error enhancing content: {str(e)}")
            return {
                'enhanced_content': doc.content,
                'sections': {},
                'compliance_requirements': [],
                'suggested_actions': []
            }
    
    def _extract_enhanced_sections(self, content: str, region: RegionType) -> Dict[str, str]:
        """Extract and categorize document sections"""
        sections = {}
        
        try:
            # Region-specific section patterns
            if region == RegionType.US:
                # CFR section patterns
                section_pattern = r'§\s*(\d+\.\d+)\s+([^\n]+)'
                matches = re.findall(section_pattern, content)
                for match in matches:
                    sections[f"section_{match[0]}"] = match[1]
            
            elif region == RegionType.EU:
                # Article patterns
                article_pattern = r'Article\s+(\d+)\s*[:\-]?\s*([^\n]+)'
                matches = re.findall(article_pattern, content, re.IGNORECASE)
                for match in matches:
                    sections[f"article_{match[0]}"] = match[1]
            
            elif region == RegionType.UK:
                # Section patterns
                section_pattern = r'(\d+)\.\s*([^\n]+)'
                matches = re.findall(section_pattern, content)
                for match in matches[:10]:  # Limit to first 10
                    sections[f"section_{match[0]}"] = match[1]
            
            elif region == RegionType.CA:
                # Section patterns
                section_pattern = r'(\d+)\.\s*([^\n]+)'
                matches = re.findall(section_pattern, content)
                for match in matches[:10]:  # Limit to first 10
                    sections[f"section_{match[0]}"] = match[1]
            
        except Exception as e:
            logger.error(f"Error extracting sections: {str(e)}")
        
        return sections
    
    def _identify_compliance_requirements(self, content: str, region: RegionType) -> List[Dict[str, Any]]:
        """Identify compliance requirements in the document"""
        requirements = []
        
        try:
            # Look for compliance-related keywords
            compliance_patterns = [
                r'shall\s+([^.]+)',
                r'must\s+([^.]+)',
                r'required\s+to\s+([^.]+)',
                r'prohibited\s+([^.]+)',
                r'forbidden\s+([^.]+)'
            ]
            
            for pattern in compliance_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches[:5]:  # Limit to 5 per pattern
                    requirements.append({
                        'type': 'mandatory' if 'shall' in pattern or 'must' in pattern else 'prohibited',
                        'description': match.strip(),
                        'region': region.value
                    })
            
        except Exception as e:
            logger.error(f"Error identifying compliance requirements: {str(e)}")
        
        return requirements
    
    def _generate_compliance_actions(self, requirements: List[Dict], title: str, region: RegionType) -> List[Dict[str, Any]]:
        """Generate suggested compliance actions"""
        actions = []
        
        try:
            for req in requirements[:3]:  # Limit to 3 actions
                action = {
                    'description': f"Review compliance with: {req['description'][:100]}...",
                    'priority': 3,  # Medium priority
                    'confidence': 0.7,
                    'explanation': f"Based on requirement identified in {title}",
                    'due_date': datetime.now() + timedelta(days=30)
                }
                actions.append(action)
                
        except Exception as e:
            logger.error(f"Error generating compliance actions: {str(e)}")
        
        return actions
    
    def _create_metadata_section(self, doc: RegulatoryDocument, sections: Dict, requirements: List) -> str:
        """Create structured metadata section"""
        try:
            metadata = [
                "\n--- DOCUMENT METADATA ---",
                f"Source: {doc.source_id}",
                f"Region: {doc.region.value}",
                f"Type: {doc.doc_type.value}",
                f"Sections: {len(sections)}",
                f"Compliance Requirements: {len(requirements)}",
                f"Keywords: {', '.join(doc.keywords[:10])}" if doc.keywords else "Keywords: None",
                f"Topics: {', '.join(doc.topics)}" if doc.topics else "Topics: None"
            ]
            
            return '\n'.join(metadata)
            
        except Exception as e:
            logger.error(f"Error creating metadata section: {str(e)}")
            return ""
    
    def _create_tags_json(self, doc: RegulatoryDocument) -> Dict[str, Any]:
        """Create tags JSON for database storage"""
        try:
            return {
                'keywords': doc.keywords or [],
                'topics': doc.topics or [],
                'sections': list(doc.sections.keys()) if doc.sections else [],
                'cross_references': doc.cites or [],
                'amendments': doc.amends or []
            }
        except Exception as e:
            logger.error(f"Error creating tags JSON: {str(e)}")
            return {}
    
    async def _get_or_create_source(self, source_id: str, region: RegionType) -> DBRegulatorySource:
        """Get or create regulatory source"""
        try:
            # Try to get existing source
            query = select(DBRegulatorySource).where(DBRegulatorySource.source_id == source_id)
            result = await self.session.execute(query)
            source = result.scalar_one_or_none()
            
            if not source:
                # Create new source
                source_names = {
                    'ecfr': 'Electronic Code of Federal Regulations',
                    'federal_register': 'Federal Register',
                    'eurlex': 'EUR-Lex',
                    'uk_legislation': 'UK Legislation',
                    'justice_laws_canada': 'Justice Laws Canada',
                    'canada_gazette': 'Canada Gazette'
                }
                
                source = DBRegulatorySource(
                    source_id=source_id,
                    name=source_names.get(source_id, source_id),
                    agency=source_id.upper(),
                    region=region.value,
                    source_type='API',
                    url='',
                    config={},
                    schedule_cron='0 6 * * *',  # Daily at 6 AM
                    requests_per_minute=60,
                    timeout_seconds=30,
                    is_active=True,
                    consecutive_failures=0,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                self.session.add(source)
                await self.session.flush()
            
            return source
            
        except Exception as e:
            logger.error(f"Error getting/creating source: {str(e)}")
            raise
    
    async def _create_actions(self, regulation_id: int, suggested_actions: List[Dict[str, Any]]):
        """Create compliance actions for a regulation"""
        try:
            for action_data in suggested_actions:
                action = DBAction(
                    regulation_id=regulation_id,
                    description=action_data['description'],
                    priority=action_data['priority'],
                    due_date=action_data.get('due_date'),
                    confidence=action_data['confidence'],
                    explanation=action_data.get('explanation'),
                    status='pending',
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                self.session.add(action)
                
        except Exception as e:
            logger.error(f"Error creating actions: {str(e)}")
    
    async def _process_cross_references(self, documents: List[RegulatoryDocument]) -> int:
        """Process cross-references between documents"""
        cross_refs_created = 0
        
        try:
            # This would implement cross-reference detection and linking
            # For now, return 0 as this requires more complex implementation
            logger.info("Cross-reference processing not yet implemented")
            
        except Exception as e:
            logger.error(f"Error processing cross-references: {str(e)}")
        
        return cross_refs_created

    async def search_documents(self, query: str, region: Optional[RegionType] = None,
                             doc_type: Optional[DocumentType] = None,
                             limit: int = 50) -> List[DBRegulation]:
        """Search documents with full-text search"""
        try:
            # Build search query
            search_query = select(DBRegulation).where(
                and_(
                    DBRegulation.is_latest_version == True,
                    or_(
                        DBRegulation.title.ilike(f'%{query}%'),
                        DBRegulation.summary.ilike(f'%{query}%'),
                        DBRegulation.full_text.ilike(f'%{query}%')
                    )
                )
            )

            # Add filters
            if region:
                search_query = search_query.where(DBRegulation.region == region.value)

            if doc_type:
                search_query = search_query.where(DBRegulation.doc_type == doc_type.value)

            # Add ordering and limit
            search_query = search_query.order_by(DBRegulation.pub_date.desc()).limit(limit)

            result = await self.session.execute(search_query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return []
