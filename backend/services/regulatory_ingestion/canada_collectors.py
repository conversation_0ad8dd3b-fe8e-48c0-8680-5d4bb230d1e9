"""
Canada Regulatory Data Collectors
Implements collectors for Justice Laws Website and Canada Gazette
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import xml.etree.ElementTree as ET
import re
from urllib.parse import urljoin, quote

from .base_collector import (
    BaseRegulatoryCollector, RegulatoryDocument, CollectionResult,
    RegionType, DocumentType
)

logger = logging.getLogger(__name__)


class JusticeLawsCollector(BaseRegulatoryCollector):
    """Collector for Justice Laws Website (laws-lois.justice.gc.ca)"""
    
    def __init__(self):
        super().__init__(
            source_name="Justice Laws Canada",
            region=RegionType.CA,
            base_url="https://laws-lois.justice.gc.ca/",
            rate_limit_per_minute=30,  # Conservative rate limit
            timeout_seconds=60
        )
        
        # Key food-related Acts and Regulations
        self.food_legislation = {
            # Acts
            "F-27": "Food and Drugs Act",
            "S-1.1": "Safe Food for Canadians Act",
            "C-0.4": "Canada Agricultural Products Act",
            "F-8": "Fisheries Act",
            "M-13": "Meat Inspection Act",
            
            # Regulations
            "C.R.C.,_c._870": "Food and Drug Regulations",
            "SOR/2018-108": "Safe Food for Canadians Regulations",
            "C.R.C.,_c._296": "Canada Agricultural Products Regulations",
            "SOR/90-288": "Meat Inspection Regulations",
            "SOR/2011-205": "Food and Drug Regulations (Nutrition Labelling)"
        }
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect current Canadian food legislation"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            for leg_id, leg_name in self.food_legislation.items():
                logger.info(f"Collecting Canadian legislation: {leg_name} ({leg_id})")
                
                doc = await self._get_justice_laws_document(leg_id, leg_name)
                if doc:
                    documents.append(doc)
                else:
                    errors.append(f"Failed to collect {leg_name} ({leg_id})")
                
                await asyncio.sleep(2)  # Rate limiting
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=24)
            )
            
        except Exception as e:
            logger.error(f"Error in Justice Laws collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=2)
            )
    
    async def _get_justice_laws_document(self, leg_id: str, leg_name: str) -> Optional[RegulatoryDocument]:
        """Get a specific document from Justice Laws"""
        try:
            # Determine if it's an Act or Regulation
            is_act = not any(char.isdigit() for char in leg_id.split('-')[0]) if '-' in leg_id else 'SOR' not in leg_id and 'C.R.C' not in leg_id
            
            # Build URL
            if is_act:
                doc_url = f"{self.base_url}eng/acts/{leg_id}/index.html"
                xml_url = f"{self.base_url}eng/acts/{leg_id}/FullText.xml"
            else:
                # Handle regulation URL format
                reg_path = leg_id.replace(',_', '/').replace('_', '-')
                doc_url = f"{self.base_url}eng/regulations/{reg_path}/index.html"
                xml_url = f"{self.base_url}eng/regulations/{reg_path}/FullText.xml"
            
            # Try to get XML version first (more structured)
            success, error, content = await self._make_request(xml_url)
            
            if not success:
                # Fall back to HTML version
                success, error, content = await self._make_request(doc_url)
                if not success:
                    logger.warning(f"Failed to get Justice Laws document {leg_id}: {error}")
                    return None
            
            # Extract text content
            text_content = self._extract_justice_laws_content(content, xml_url if success else doc_url)
            
            # Get version information
            version_info = await self._get_version_info(leg_id, is_act)
            
            return RegulatoryDocument(
                source_id="justice_laws_canada",
                title=leg_name,
                document_id=leg_id,
                url=doc_url,
                content=self._normalize_content(text_content),
                summary=self._generate_summary(text_content),
                region=RegionType.CA,
                doc_type=DocumentType.ACT if is_act else DocumentType.REGULATION,
                effective_date=version_info.get('effective_date'),
                published_date=version_info.get('published_date') or datetime.now(),
                last_modified=version_info.get('last_modified'),
                version=version_info.get('version', 'current'),
                language="en",
                sections=self._extract_justice_laws_sections(content),
                keywords=self._extract_food_keywords(text_content),
                topics=self._classify_food_topics(text_content, leg_name)
            )
            
        except Exception as e:
            logger.error(f"Error getting Justice Laws document {leg_id}: {str(e)}")
            return None
    
    def _extract_justice_laws_content(self, content: str, url: str) -> str:
        """Extract text content from Justice Laws XML or HTML"""
        try:
            if url.endswith('.xml'):
                # XML content
                return self._extract_text_from_xml(content)
            else:
                # HTML content
                return self._extract_text_from_html(content)
        except Exception as e:
            logger.error(f"Error extracting Justice Laws content: {str(e)}")
            return content
    
    def _extract_text_from_xml(self, xml_content: str) -> str:
        """Extract text from Justice Laws XML"""
        try:
            # Remove XML tags and extract meaningful text
            import re
            
            # Remove XML declarations and processing instructions
            text = re.sub(r'<\?[^>]+\?>', '', xml_content)
            
            # Remove XML tags but keep content
            text = re.sub(r'<[^>]+>', ' ', text)
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from Justice Laws XML: {str(e)}")
            return xml_content
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """Extract text from Justice Laws HTML"""
        try:
            import re
            
            # Remove script and style elements
            text = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
            
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', ' ', text)
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from Justice Laws HTML: {str(e)}")
            return html_content
    
    def _extract_justice_laws_sections(self, content: str) -> Dict[str, str]:
        """Extract sections from Justice Laws content"""
        sections = {}
        try:
            # Look for section patterns in the content
            import re
            
            # Pattern for sections (simplified)
            section_pattern = r'(\d+\.?\s*[^\n]+)'
            matches = re.findall(section_pattern, content)
            
            for i, match in enumerate(matches[:20]):  # Limit to first 20 sections
                sections[f"section_{i+1}"] = match.strip()
                
        except Exception as e:
            logger.error(f"Error extracting Justice Laws sections: {str(e)}")
        
        return sections
    
    async def _get_version_info(self, leg_id: str, is_act: bool) -> Dict[str, Any]:
        """Get version information for a Justice Laws document"""
        try:
            # Try to get version history
            if is_act:
                version_url = f"{self.base_url}eng/acts/{leg_id}/page-1.html#docCont"
            else:
                reg_path = leg_id.replace(',_', '/').replace('_', '-')
                version_url = f"{self.base_url}eng/regulations/{reg_path}/page-1.html#docCont"
            
            success, error, content = await self._make_request(version_url)
            
            if success and content:
                # Extract version information from HTML
                return self._parse_version_info(content)
            
        except Exception as e:
            logger.error(f"Error getting version info for {leg_id}: {str(e)}")
        
        return {}
    
    def _parse_version_info(self, html_content: str) -> Dict[str, Any]:
        """Parse version information from Justice Laws HTML"""
        version_info = {}
        try:
            import re
            
            # Look for date patterns
            date_pattern = r'(\d{4}-\d{2}-\d{2})'
            dates = re.findall(date_pattern, html_content)
            
            if dates:
                # Use the most recent date as last modified
                latest_date = max(dates)
                version_info['last_modified'] = datetime.strptime(latest_date, '%Y-%m-%d')
                version_info['published_date'] = version_info['last_modified']
            
            # Look for version information
            version_pattern = r'version[:\s]+([^\s<]+)'
            version_match = re.search(version_pattern, html_content, re.IGNORECASE)
            if version_match:
                version_info['version'] = version_match.group(1)
                
        except Exception as e:
            logger.error(f"Error parsing version info: {str(e)}")
        
        return version_info
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect changes in Canadian legislation since given date"""
        if not since_date:
            since_date = datetime.now() - timedelta(days=30)
        
        # This would check version history for each document
        # Implementation would compare current version with stored version
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific Justice Laws document by ID"""
        try:
            # Check if it's a known document
            if document_id in self.food_legislation:
                return await self._get_justice_laws_document(
                    document_id, 
                    self.food_legislation[document_id]
                )
            else:
                # Try to fetch unknown document
                return await self._get_justice_laws_document(document_id, f"Document {document_id}")
                
        except Exception as e:
            logger.error(f"Error getting Justice Laws document {document_id}: {str(e)}")
            return None


class CanadaGazetteCollector(BaseRegulatoryCollector):
    """Collector for Canada Gazette (gazette.gc.ca)"""
    
    def __init__(self):
        super().__init__(
            source_name="Canada Gazette",
            region=RegionType.CA,
            base_url="https://gazette.gc.ca/",
            rate_limit_per_minute=30,
            timeout_seconds=45
        )
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect recent Canada Gazette publications"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            # Search for food-related gazette notices from last 90 days
            since_date = datetime.now() - timedelta(days=90)
            
            # Search both Part I (proposed) and Part II (final) regulations
            for part in ['rp-pr', 'rg-rg']:  # Part I and Part II
                logger.info(f"Collecting Canada Gazette {part}")
                
                part_docs = await self._search_gazette_part(part, since_date)
                documents.extend(part_docs)
                
                await asyncio.sleep(2)  # Rate limiting
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=12)
            )
            
        except Exception as e:
            logger.error(f"Error in Canada Gazette collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=2)
            )
    
    async def _search_gazette_part(self, part: str, since_date: datetime) -> List[RegulatoryDocument]:
        """Search a specific part of the Canada Gazette"""
        documents = []
        
        try:
            # Build search URL for food-related content
            search_url = f"{self.base_url}{part}/search-recherche"
            
            # Search parameters
            params = {
                'q': 'food OR safety OR nutrition OR labelling OR CFIA OR "food and drug"',
                'date_start': since_date.strftime('%Y-%m-%d'),
                'date_end': datetime.now().strftime('%Y-%m-%d'),
                'format': 'rss'
            }
            
            success, error, content = await self._make_request(search_url, params=params)
            
            if success and content:
                # Parse RSS feed
                gazette_items = self._parse_gazette_rss(content)
                
                for item in gazette_items:
                    doc = await self._get_gazette_document(item, part)
                    if doc:
                        documents.append(doc)
                    
                    await asyncio.sleep(1)  # Rate limiting
            
        except Exception as e:
            logger.error(f"Error searching Canada Gazette {part}: {str(e)}")
        
        return documents
    
    def _parse_gazette_rss(self, rss_content: str) -> List[Dict]:
        """Parse Canada Gazette RSS feed"""
        items = []
        try:
            root = ET.fromstring(rss_content)
            
            for item in root.findall('.//item'):
                title_elem = item.find('title')
                link_elem = item.find('link')
                pub_date_elem = item.find('pubDate')
                description_elem = item.find('description')
                
                if title_elem is not None and link_elem is not None:
                    items.append({
                        'title': title_elem.text,
                        'link': link_elem.text,
                        'pub_date': pub_date_elem.text if pub_date_elem is not None else None,
                        'description': description_elem.text if description_elem is not None else None
                    })
                    
        except ET.ParseError as e:
            logger.error(f"Error parsing Canada Gazette RSS: {str(e)}")
        
        return items
    
    async def _get_gazette_document(self, item: Dict, part: str) -> Optional[RegulatoryDocument]:
        """Get full Canada Gazette document"""
        try:
            doc_url = item.get('link', '')
            if not doc_url:
                return None
            
            # Get document content
            success, error, content = await self._make_request(doc_url)
            
            if not success or not content:
                logger.warning(f"Failed to get Gazette document {doc_url}: {error}")
                return None
            
            # Extract text content
            text_content = self._extract_text_from_html(content)
            
            # Parse publication date
            pub_date = self._parse_gazette_date(item.get('pub_date'))
            
            # Extract document ID from URL
            doc_id = self._extract_gazette_document_id(doc_url)
            
            return RegulatoryDocument(
                source_id="canada_gazette",
                title=item.get('title', ''),
                document_id=doc_id,
                url=doc_url,
                content=self._normalize_content(text_content),
                summary=item.get('description', '')[:500] or self._generate_summary(text_content),
                region=RegionType.CA,
                doc_type=DocumentType.NOTICE if part == 'rp-pr' else DocumentType.REGULATION,
                effective_date=None,  # Would need additional parsing
                published_date=pub_date or datetime.now(),
                last_modified=None,
                version="1.0",
                language="en",
                keywords=self._extract_food_keywords(text_content),
                topics=self._classify_food_topics(text_content, item.get('title', ''))
            )
            
        except Exception as e:
            logger.error(f"Error getting Gazette document: {str(e)}")
            return None
    
    def _parse_gazette_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse Canada Gazette date string"""
        if not date_str:
            return None
        try:
            # Try RFC 2822 format first (RSS standard)
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_str)
        except (ValueError, TypeError):
            try:
                # Try ISO format
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            except ValueError:
                return None
    
    def _extract_gazette_document_id(self, url: str) -> str:
        """Extract document ID from Canada Gazette URL"""
        try:
            # Extract meaningful ID from URL
            parts = url.split('/')
            if len(parts) >= 2:
                return f"gazette_{parts[-2]}_{parts[-1]}"
            return url.split('/')[-1]
        except Exception:
            return url
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect new Canada Gazette publications since given date"""
        if not since_date:
            since_date = datetime.now() - timedelta(days=7)
        
        # This would be similar to collect_current_regulations but with date filter
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific Canada Gazette document by ID"""
        try:
            # Reconstruct URL from document ID
            if document_id.startswith('gazette_'):
                parts = document_id.replace('gazette_', '').split('_')
                if len(parts) >= 2:
                    url = f"{self.base_url}{parts[0]}/{parts[1]}"
                    
                    item = {
                        'title': f'Canada Gazette {document_id}',
                        'link': url,
                        'pub_date': datetime.now().isoformat(),
                        'description': ''
                    }
                    
                    return await self._get_gazette_document(item, parts[0])
                    
        except Exception as e:
            logger.error(f"Error getting Gazette document {document_id}: {str(e)}")
            
        return None
