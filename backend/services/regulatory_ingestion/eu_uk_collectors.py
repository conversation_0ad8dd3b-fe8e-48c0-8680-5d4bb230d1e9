"""
EU and UK Regulatory Data Collectors
Implements collectors for EUR-Lex and UK Legislation.gov.uk APIs
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import xml.etree.ElementTree as ET
import re
from urllib.parse import urljoin, quote

from .base_collector import (
    BaseRegulatoryCollector, RegulatoryDocument, CollectionResult,
    RegionType, DocumentType
)

logger = logging.getLogger(__name__)


class EURLexCollector(BaseRegulatoryCollector):
    """Collector for EUR-Lex (European Union legal database)"""
    
    def __init__(self):
        super().__init__(
            source_name="EUR-Lex",
            region=RegionType.EU,
            base_url="https://eur-lex.europa.eu/",
            rate_limit_per_minute=30,  # Conservative rate limit
            timeout_seconds=60
        )
        
        # Food-related document types and sectors
        self.food_sectors = [
            "15",  # Food industry
            "13",  # Public health
            "04"   # Agriculture
        ]
        
        self.document_types = [
            "32",  # Regulations
            "31",  # Directives
            "52"   # EFSA opinions
        ]
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect current EU food regulations and directives"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            # Search for food-related regulations from last 2 years
            since_date = datetime.now() - timedelta(days=730)
            
            for doc_type in self.document_types:
                logger.info(f"Collecting EUR-Lex documents of type {doc_type}")
                
                # Build search query
                search_results = await self._search_eurlex_documents(
                    doc_type=doc_type,
                    since_date=since_date,
                    sectors=self.food_sectors
                )
                
                if search_results:
                    type_docs = await self._process_eurlex_results(search_results)
                    documents.extend(type_docs)
                
                await asyncio.sleep(2)  # Rate limiting between document types
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=24)
            )
            
        except Exception as e:
            logger.error(f"Error in EUR-Lex collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=2)
            )
    
    async def _search_eurlex_documents(self, doc_type: str, since_date: datetime, 
                                     sectors: List[str]) -> Optional[List[Dict]]:
        """Search EUR-Lex for documents"""
        try:
            # EUR-Lex SPARQL endpoint for searching
            sparql_endpoint = "https://eur-lex.europa.eu/sparql"
            
            # Build SPARQL query for food-related documents
            query = f"""
            PREFIX cdm: <http://publications.europa.eu/ontology/cdm#>
            PREFIX skos: <http://www.w3.org/2004/02/skos/core#>
            
            SELECT DISTINCT ?work ?celex ?title ?date ?type WHERE {{
                ?work cdm:work_has_resource-type ?type .
                ?work cdm:resource_legal_id_celex ?celex .
                ?work cdm:work_date_document ?date .
                ?work cdm:expression_title ?title .
                
                FILTER(?date >= "{since_date.strftime('%Y-%m-%d')}"^^xsd:date)
                FILTER(CONTAINS(?celex, "{doc_type}"))
                FILTER(LANG(?title) = "en")
                
                # Filter for food-related content
                {{
                    ?work cdm:work_is_about_concept_eurovoc ?concept .
                    ?concept skos:notation ?sector .
                    FILTER(?sector IN ({', '.join([f'"{s}"' for s in sectors])}))
                }}
                UNION
                {{
                    FILTER(CONTAINS(LCASE(?title), "food") || 
                           CONTAINS(LCASE(?title), "safety") ||
                           CONTAINS(LCASE(?title), "efsa"))
                }}
            }}
            ORDER BY DESC(?date)
            LIMIT 100
            """
            
            # Make SPARQL request
            params = {
                'query': query,
                'format': 'application/sparql-results+json'
            }
            
            success, error, data = await self._make_request(sparql_endpoint, params=params)
            
            if success and data and 'results' in data:
                return data['results']['bindings']
            else:
                logger.warning(f"EUR-Lex search failed: {error}")
                return None
                
        except Exception as e:
            logger.error(f"Error searching EUR-Lex: {str(e)}")
            return None
    
    async def _process_eurlex_results(self, results: List[Dict]) -> List[RegulatoryDocument]:
        """Process EUR-Lex search results into documents"""
        documents = []
        
        for result in results:
            try:
                celex_id = result.get('celex', {}).get('value', '')
                if not celex_id:
                    continue
                
                doc = await self._get_eurlex_document(celex_id, result)
                if doc:
                    documents.append(doc)
                
                await asyncio.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error processing EUR-Lex result: {str(e)}")
        
        return documents
    
    async def _get_eurlex_document(self, celex_id: str, search_result: Dict) -> Optional[RegulatoryDocument]:
        """Get full EUR-Lex document by CELEX ID"""
        try:
            # Get document metadata and content
            doc_url = f"https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:{celex_id}"
            
            # Try to get consolidated version if available
            consolidated_url = f"https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:{celex_id}&qid=consolidated"
            
            success, error, content = await self._make_request(consolidated_url)
            if not success:
                # Fall back to original version
                success, error, content = await self._make_request(doc_url)
            
            if not success or not content:
                logger.warning(f"Failed to get EUR-Lex document {celex_id}: {error}")
                return None
            
            # Extract text content from HTML
            text_content = self._extract_text_from_html(content)
            
            # Extract metadata from search result
            title = search_result.get('title', {}).get('value', '')
            date_str = search_result.get('date', {}).get('value', '')
            
            # Parse date
            pub_date = self._parse_eurlex_date(date_str)
            
            # Determine document type
            doc_type = self._determine_eurlex_doc_type(celex_id, title)
            
            return RegulatoryDocument(
                source_id="eurlex",
                title=title,
                document_id=celex_id,
                url=doc_url,
                content=self._normalize_content(text_content),
                summary=self._generate_summary(text_content),
                region=RegionType.EU,
                doc_type=doc_type,
                effective_date=None,  # Would need additional parsing
                published_date=pub_date or datetime.now(),
                last_modified=None,
                version="current",
                language="en",
                keywords=self._extract_food_keywords(text_content),
                topics=self._classify_food_topics(text_content, title)
            )
            
        except Exception as e:
            logger.error(f"Error getting EUR-Lex document {celex_id}: {str(e)}")
            return None
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """Extract text content from EUR-Lex HTML"""
        try:
            # Remove HTML tags and extract meaningful text
            import re
            
            # Remove script and style elements
            text = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
            
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', ' ', text)
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from EUR-Lex HTML: {str(e)}")
            return html_content
    
    def _parse_eurlex_date(self, date_str: str) -> Optional[datetime]:
        """Parse EUR-Lex date string"""
        if not date_str:
            return None
        try:
            # EUR-Lex typically uses ISO format
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except ValueError:
            try:
                return datetime.strptime(date_str[:10], '%Y-%m-%d')
            except ValueError:
                return None
    
    def _determine_eurlex_doc_type(self, celex_id: str, title: str) -> DocumentType:
        """Determine document type from CELEX ID and title"""
        if celex_id.startswith('32'):
            return DocumentType.REGULATION
        elif celex_id.startswith('31'):
            return DocumentType.DIRECTIVE
        elif 'efsa' in title.lower() or 'opinion' in title.lower():
            return DocumentType.OPINION
        else:
            return DocumentType.REGULATION
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect changes in EU regulations since given date"""
        if not since_date:
            since_date = datetime.now() - timedelta(days=7)
        
        # This would search for amendments and modifications
        # Implementation would use EUR-Lex relationship data
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific EUR-Lex document by CELEX ID"""
        try:
            # Create a mock search result for the document
            search_result = {
                'celex': {'value': document_id},
                'title': {'value': f'Document {document_id}'},
                'date': {'value': datetime.now().isoformat()}
            }
            
            return await self._get_eurlex_document(document_id, search_result)
            
        except Exception as e:
            logger.error(f"Error getting EUR-Lex document {document_id}: {str(e)}")
            return None


class UKLegislationCollector(BaseRegulatoryCollector):
    """Collector for UK Legislation.gov.uk API"""
    
    def __init__(self):
        super().__init__(
            source_name="UK Legislation",
            region=RegionType.UK,
            base_url="https://www.legislation.gov.uk/",
            rate_limit_per_minute=60,
            timeout_seconds=45
        )
        
        # Food-related legislation types
        self.legislation_types = [
            "uksi",  # Statutory Instruments
            "ukpga", # Acts of Parliament
            "eur"    # Retained EU law
        ]
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect current UK food legislation"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            # Search for food-related legislation from last year
            since_date = datetime.now() - timedelta(days=365)
            
            for leg_type in self.legislation_types:
                logger.info(f"Collecting UK legislation of type {leg_type}")
                
                search_results = await self._search_uk_legislation(
                    leg_type=leg_type,
                    since_date=since_date
                )
                
                if search_results:
                    type_docs = await self._process_uk_results(search_results)
                    documents.extend(type_docs)
                
                await asyncio.sleep(2)  # Rate limiting
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=24)
            )
            
        except Exception as e:
            logger.error(f"Error in UK Legislation collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=2)
            )
    
    async def _search_uk_legislation(self, leg_type: str, since_date: datetime) -> Optional[List[Dict]]:
        """Search UK legislation for food-related documents"""
        try:
            # UK Legislation search API
            search_url = f"{self.base_url}{leg_type}"
            
            params = {
                'text': 'food OR safety OR nutrition OR labelling OR hygiene',
                'year': f"{since_date.year}-{datetime.now().year}",
                'format': 'atom'
            }
            
            success, error, content = await self._make_request(search_url, params=params)
            
            if success and content:
                # Parse Atom feed
                return self._parse_uk_atom_feed(content)
            else:
                logger.warning(f"UK legislation search failed: {error}")
                return None
                
        except Exception as e:
            logger.error(f"Error searching UK legislation: {str(e)}")
            return None
    
    def _parse_uk_atom_feed(self, atom_content: str) -> List[Dict]:
        """Parse UK legislation Atom feed"""
        try:
            root = ET.fromstring(atom_content)
            entries = []
            
            # Parse Atom namespace
            ns = {'atom': 'http://www.w3.org/2005/Atom'}
            
            for entry in root.findall('.//atom:entry', ns):
                title_elem = entry.find('atom:title', ns)
                link_elem = entry.find('atom:link[@rel="alternate"]', ns)
                updated_elem = entry.find('atom:updated', ns)
                
                if title_elem is not None and link_elem is not None:
                    entries.append({
                        'title': title_elem.text,
                        'link': link_elem.get('href'),
                        'updated': updated_elem.text if updated_elem is not None else None
                    })
            
            return entries
            
        except ET.ParseError as e:
            logger.error(f"Error parsing UK legislation Atom feed: {str(e)}")
            return []
    
    async def _process_uk_results(self, results: List[Dict]) -> List[RegulatoryDocument]:
        """Process UK legislation search results"""
        documents = []
        
        for result in results:
            try:
                doc = await self._get_uk_document(result)
                if doc:
                    documents.append(doc)
                
                await asyncio.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error processing UK legislation result: {str(e)}")
        
        return documents
    
    async def _get_uk_document(self, result: Dict) -> Optional[RegulatoryDocument]:
        """Get full UK legislation document"""
        try:
            doc_url = result.get('link', '')
            if not doc_url:
                return None
            
            # Get document content
            content_url = f"{doc_url}/data.xml"
            success, error, content = await self._make_request(content_url)
            
            if not success:
                # Try HTML version
                success, error, content = await self._make_request(doc_url)
            
            if not success or not content:
                logger.warning(f"Failed to get UK document {doc_url}: {error}")
                return None
            
            # Extract text content
            text_content = self._extract_uk_text_content(content)
            
            # Extract document ID from URL
            doc_id = self._extract_uk_document_id(doc_url)
            
            # Parse date
            updated_date = self._parse_uk_date(result.get('updated'))
            
            return RegulatoryDocument(
                source_id="uk_legislation",
                title=result.get('title', ''),
                document_id=doc_id,
                url=doc_url,
                content=self._normalize_content(text_content),
                summary=self._generate_summary(text_content),
                region=RegionType.UK,
                doc_type=self._determine_uk_doc_type(doc_url),
                effective_date=None,
                published_date=updated_date or datetime.now(),
                last_modified=updated_date,
                version="current",
                language="en",
                keywords=self._extract_food_keywords(text_content),
                topics=self._classify_food_topics(text_content, result.get('title', ''))
            )
            
        except Exception as e:
            logger.error(f"Error getting UK document: {str(e)}")
            return None
    
    def _extract_uk_text_content(self, content: str) -> str:
        """Extract text from UK legislation XML/HTML"""
        try:
            if content.strip().startswith('<?xml') or content.strip().startswith('<'):
                # XML content - extract text
                import re
                text = re.sub(r'<[^>]+>', ' ', content)
                text = re.sub(r'\s+', ' ', text)
                return text.strip()
            else:
                return content
        except Exception:
            return content
    
    def _extract_uk_document_id(self, url: str) -> str:
        """Extract document ID from UK legislation URL"""
        try:
            # Extract from URL pattern like /uksi/2021/123
            parts = url.split('/')
            if len(parts) >= 4:
                return f"{parts[-3]}_{parts[-2]}_{parts[-1]}"
            return url.split('/')[-1]
        except Exception:
            return url
    
    def _parse_uk_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse UK legislation date"""
        if not date_str:
            return None
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except ValueError:
            return None
    
    def _determine_uk_doc_type(self, url: str) -> DocumentType:
        """Determine UK document type from URL"""
        if '/uksi/' in url:
            return DocumentType.REGULATION
        elif '/ukpga/' in url:
            return DocumentType.ACT
        elif '/eur/' in url:
            return DocumentType.REGULATION
        else:
            return DocumentType.REGULATION
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect changes in UK legislation since given date"""
        if not since_date:
            since_date = datetime.now() - timedelta(days=7)
        
        # This would search for recent amendments
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific UK legislation document by ID"""
        try:
            # Reconstruct URL from document ID
            parts = document_id.split('_')
            if len(parts) >= 3:
                url = f"{self.base_url}{parts[0]}/{parts[1]}/{parts[2]}"
                
                result = {
                    'title': f'UK Legislation {document_id}',
                    'link': url,
                    'updated': datetime.now().isoformat()
                }
                
                return await self._get_uk_document(result)
                
        except Exception as e:
            logger.error(f"Error getting UK document {document_id}: {str(e)}")
            
        return None
