"""
US Regulatory Data Collectors
Implements collectors for eCFR and Federal Register APIs
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import xml.etree.ElementTree as ET
import re
from urllib.parse import urljoin

from .base_collector import (
    BaseRegulatoryCollector, RegulatoryDocument, CollectionResult,
    RegionType, DocumentType
)

logger = logging.getLogger(__name__)


class ECFRCollector(BaseRegulatoryCollector):
    """Collector for Electronic Code of Federal Regulations (eCFR)"""
    
    def __init__(self):
        super().__init__(
            source_name="eCFR",
            region=RegionType.US,
            base_url="https://www.ecfr.gov/api/versioner/v1/",
            rate_limit_per_minute=120,  # eCFR allows higher rate
            timeout_seconds=45
        )
        
        # Focus on food-related CFR titles
        self.target_titles = {
            "21": "Food and Drugs (FDA)",
            "9": "Animals and Animal Products (USDA-FSIS)"
        }
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect current CFR regulations for food-related titles"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            for title_num, title_name in self.target_titles.items():
                logger.info(f"Collecting eCFR Title {title_num}: {title_name}")
                
                # Get structure for this title
                success, error, structure = await self._get_title_structure(title_num)
                if not success:
                    errors.append(f"Failed to get structure for Title {title_num}: {error}")
                    continue
                
                # Collect parts within this title
                title_docs = await self._collect_title_parts(title_num, structure)
                documents.extend(title_docs)
                
                # Add delay between titles to be respectful
                await asyncio.sleep(1)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,  # Will be calculated during storage
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=24)
            )
            
        except Exception as e:
            logger.error(f"Error in eCFR collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=1)
            )
    
    async def _get_title_structure(self, title_num: str) -> tuple[bool, Optional[str], Optional[Dict]]:
        """Get the structure of a CFR title"""
        url = f"{self.base_url}structure/{title_num}"
        return await self._make_request(url)
    
    async def _collect_title_parts(self, title_num: str, structure: Dict) -> List[RegulatoryDocument]:
        """Collect all parts within a CFR title"""
        documents = []
        
        if not structure or 'children' not in structure:
            return documents
        
        # Navigate through structure: Title -> Chapters -> Parts -> Sections
        for chapter in structure.get('children', []):
            for part in chapter.get('children', []):
                part_num = part.get('identifier', '')
                part_name = part.get('label_description', '')
                
                # Get full text for this part
                part_doc = await self._get_part_content(title_num, part_num, part_name)
                if part_doc:
                    documents.append(part_doc)
                
                # Small delay between parts
                await asyncio.sleep(0.5)
        
        return documents
    
    async def _get_part_content(self, title_num: str, part_num: str, part_name: str) -> Optional[RegulatoryDocument]:
        """Get the full content of a CFR part"""
        try:
            # Get current version of the part
            url = f"{self.base_url}full/{title_num}/{part_num}"
            success, error, data = await self._make_request(url)
            
            if not success or not data:
                logger.warning(f"Failed to get content for {title_num} CFR {part_num}: {error}")
                return None
            
            # Extract content from XML structure
            content = self._extract_cfr_content(data)
            if not content:
                return None
            
            # Create document
            document_id = f"{title_num}_CFR_{part_num}"
            url_link = f"https://www.ecfr.gov/current/title-{title_num}/part-{part_num}"
            
            return RegulatoryDocument(
                source_id="ecfr",
                title=f"{title_num} CFR {part_num} - {part_name}",
                document_id=document_id,
                url=url_link,
                content=self._normalize_content(content),
                summary=self._generate_summary(content),
                region=RegionType.US,
                doc_type=DocumentType.REGULATION,
                effective_date=None,  # CFR is current law
                published_date=datetime.now(),
                last_modified=None,
                version="current",
                language="en",
                sections=self._extract_cfr_sections(data),
                keywords=self._extract_food_keywords(content),
                topics=self._classify_food_topics(content, part_name)
            )
            
        except Exception as e:
            logger.error(f"Error getting CFR part {title_num}/{part_num}: {str(e)}")
            return None
    
    def _extract_cfr_content(self, data: Dict) -> str:
        """Extract readable content from eCFR API response"""
        try:
            # eCFR API returns structured data - extract text content
            if 'content' in data:
                return self._extract_text_from_structure(data['content'])
            elif 'text' in data:
                return data['text']
            else:
                return str(data)
        except Exception as e:
            logger.error(f"Error extracting CFR content: {str(e)}")
            return ""
    
    def _extract_text_from_structure(self, content_structure: Any) -> str:
        """Recursively extract text from nested content structure"""
        if isinstance(content_structure, str):
            return content_structure
        elif isinstance(content_structure, dict):
            text_parts = []
            for key, value in content_structure.items():
                if key in ['text', 'content', 'label']:
                    text_parts.append(str(value))
                elif isinstance(value, (dict, list)):
                    text_parts.append(self._extract_text_from_structure(value))
            return ' '.join(text_parts)
        elif isinstance(content_structure, list):
            return ' '.join([self._extract_text_from_structure(item) for item in content_structure])
        else:
            return str(content_structure)
    
    def _extract_cfr_sections(self, data: Dict) -> Dict[str, str]:
        """Extract individual sections from CFR data"""
        sections = {}
        try:
            # Implementation depends on eCFR API structure
            # This is a simplified version
            if 'content' in data and isinstance(data['content'], dict):
                for section_id, section_content in data['content'].items():
                    if isinstance(section_content, dict) and 'text' in section_content:
                        sections[section_id] = section_content['text']
        except Exception as e:
            logger.error(f"Error extracting CFR sections: {str(e)}")
        
        return sections
    
    def _extract_food_keywords(self, content: str) -> List[str]:
        """Extract food-related keywords from content"""
        food_keywords = [
            'food safety', 'HACCP', 'allergen', 'labeling', 'nutrition',
            'additive', 'preservative', 'contamination', 'pathogen',
            'inspection', 'recall', 'adulteration', 'misbranding',
            'organic', 'pesticide', 'residue', 'tolerance', 'GMP',
            'sanitation', 'facility', 'processing', 'packaging'
        ]
        
        content_lower = content.lower()
        found_keywords = []
        
        for keyword in food_keywords:
            if keyword.lower() in content_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _classify_food_topics(self, content: str, part_name: str) -> List[str]:
        """Classify content into food regulatory topics"""
        topics = []
        content_lower = content.lower()
        part_lower = part_name.lower()
        
        topic_patterns = {
            'Food Safety': ['safety', 'haccp', 'pathogen', 'contamination', 'sanitation'],
            'Labeling': ['label', 'nutrition facts', 'ingredient', 'claim'],
            'Additives/Chemicals': ['additive', 'preservative', 'chemical', 'substance'],
            'Organic/Natural': ['organic', 'natural', 'certified'],
            'Import/Export': ['import', 'export', 'foreign', 'international'],
            'Inspection': ['inspect', 'audit', 'compliance', 'enforcement'],
            'Manufacturing': ['processing', 'manufacturing', 'facility', 'equipment']
        }
        
        for topic, patterns in topic_patterns.items():
            if any(pattern in content_lower or pattern in part_lower for pattern in patterns):
                topics.append(topic)
        
        return topics if topics else ['General']
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect changes in CFR since given date"""
        # eCFR provides version history - this would compare versions
        # For now, return empty list as this requires more complex implementation
        logger.info("CFR change detection not yet implemented")
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific CFR document by ID"""
        try:
            # Parse document ID (format: "21_CFR_101")
            parts = document_id.split('_')
            if len(parts) >= 3 and parts[1] == 'CFR':
                title_num = parts[0]
                part_num = parts[2]
                
                # Get part name from structure
                success, error, structure = await self._get_title_structure(title_num)
                if success and structure:
                    part_name = self._find_part_name(structure, part_num)
                    return await self._get_part_content(title_num, part_num, part_name or "")
                    
        except Exception as e:
            logger.error(f"Error getting CFR document {document_id}: {str(e)}")
        
        return None
    
    def _find_part_name(self, structure: Dict, part_num: str) -> Optional[str]:
        """Find the name of a part in the CFR structure"""
        try:
            for chapter in structure.get('children', []):
                for part in chapter.get('children', []):
                    if part.get('identifier') == part_num:
                        return part.get('label_description', '')
        except Exception:
            pass
        return None


class FederalRegisterCollector(BaseRegulatoryCollector):
    """Collector for Federal Register API"""
    
    def __init__(self):
        super().__init__(
            source_name="Federal Register",
            region=RegionType.US,
            base_url="https://www.federalregister.gov/api/v1/",
            rate_limit_per_minute=60,
            timeout_seconds=30
        )
    
    async def collect_current_regulations(self) -> CollectionResult:
        """Collect recent Federal Register documents affecting food regulations"""
        start_time = datetime.now()
        documents = []
        errors = []
        
        try:
            # Search for food-related documents from last 30 days
            since_date = datetime.now() - timedelta(days=30)
            
            # Search parameters for food-related content
            search_params = {
                'conditions[publication_date][gte]': since_date.strftime('%Y-%m-%d'),
                'conditions[agencies][]': ['food-and-drug-administration', 'food-safety-and-inspection-service'],
                'conditions[type][]': ['RULE', 'PRORULE', 'NOTICE'],
                'per_page': 100,
                'page': 1
            }
            
            page = 1
            while True:
                search_params['page'] = page
                success, error, data = await self._make_request(
                    f"{self.base_url}documents.json",
                    params=search_params
                )
                
                if not success or not data:
                    errors.append(f"Failed to get Federal Register page {page}: {error}")
                    break
                
                page_docs = await self._process_federal_register_page(data)
                documents.extend(page_docs)
                
                # Check if there are more pages
                if not data.get('results') or len(data['results']) < search_params['per_page']:
                    break
                
                page += 1
                await asyncio.sleep(1)  # Rate limiting
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return CollectionResult(
                success=len(errors) == 0,
                documents_collected=len(documents),
                documents_updated=0,
                documents_new=len(documents),
                errors=errors,
                duration_seconds=duration,
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=6)
            )
            
        except Exception as e:
            logger.error(f"Error in Federal Register collection: {str(e)}")
            return CollectionResult(
                success=False,
                documents_collected=0,
                documents_updated=0,
                documents_new=0,
                errors=[str(e)],
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                last_collection_time=datetime.now(),
                next_collection_time=datetime.now() + timedelta(hours=1)
            )
    
    async def _process_federal_register_page(self, data: Dict) -> List[RegulatoryDocument]:
        """Process a page of Federal Register results"""
        documents = []
        
        for result in data.get('results', []):
            try:
                doc = await self._create_federal_register_document(result)
                if doc:
                    documents.append(doc)
            except Exception as e:
                logger.error(f"Error processing Federal Register document: {str(e)}")
        
        return documents
    
    async def _create_federal_register_document(self, result: Dict) -> Optional[RegulatoryDocument]:
        """Create a RegulatoryDocument from Federal Register API result"""
        try:
            # Get full document content
            document_number = result.get('document_number')
            if not document_number:
                return None
            
            # Fetch full document
            success, error, full_doc = await self._make_request(
                f"{self.base_url}documents/{document_number}.json"
            )
            
            if not success or not full_doc:
                logger.warning(f"Failed to get full Federal Register document {document_number}")
                return None
            
            # Extract document data
            title = full_doc.get('title', result.get('title', ''))
            abstract = full_doc.get('abstract', '')
            full_text = full_doc.get('full_text_xml_url', '')
            
            # Get full text if available
            content = abstract
            if full_text:
                text_success, text_error, text_content = await self._make_request(full_text)
                if text_success and text_content:
                    content = self._extract_text_from_xml(text_content)
            
            # Determine document type
            doc_type = self._map_federal_register_type(result.get('type', ''))
            
            # Parse dates
            publication_date = self._parse_date(result.get('publication_date'))
            effective_date = self._parse_date(full_doc.get('effective_on'))
            
            return RegulatoryDocument(
                source_id="federal_register",
                title=title,
                document_id=document_number,
                url=result.get('html_url', ''),
                content=self._normalize_content(content),
                summary=abstract[:500] if abstract else self._generate_summary(content),
                region=RegionType.US,
                doc_type=doc_type,
                effective_date=effective_date,
                published_date=publication_date or datetime.now(),
                last_modified=None,
                version="1.0",
                language="en",
                keywords=self._extract_food_keywords(content),
                topics=self._classify_food_topics(content, title),
                amends=self._extract_cfr_amendments(full_doc)
            )
            
        except Exception as e:
            logger.error(f"Error creating Federal Register document: {str(e)}")
            return None
    
    def _extract_text_from_xml(self, xml_content: str) -> str:
        """Extract text content from Federal Register XML"""
        try:
            # Remove XML tags and extract text
            import re
            text = re.sub(r'<[^>]+>', ' ', xml_content)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        except Exception:
            return xml_content
    
    def _map_federal_register_type(self, fr_type: str) -> DocumentType:
        """Map Federal Register type to our DocumentType"""
        type_mapping = {
            'RULE': DocumentType.REGULATION,
            'PRORULE': DocumentType.REGULATION,
            'NOTICE': DocumentType.NOTICE,
            'PRESDOCU': DocumentType.NOTICE
        }
        return type_mapping.get(fr_type, DocumentType.NOTICE)
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime"""
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            try:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            except ValueError:
                return None
    
    def _extract_cfr_amendments(self, full_doc: Dict) -> List[str]:
        """Extract CFR parts that this document amends"""
        amendments = []
        try:
            # Look for CFR citations in the document
            cfr_parts = full_doc.get('cfr_references', [])
            for cfr_ref in cfr_parts:
                title = cfr_ref.get('title')
                part = cfr_ref.get('part')
                if title and part:
                    amendments.append(f"{title}_CFR_{part}")
        except Exception:
            pass
        return amendments
    
    async def detect_changes(self, since_date: Optional[datetime] = None) -> List[RegulatoryDocument]:
        """Detect new Federal Register documents since given date"""
        if not since_date:
            since_date = datetime.now() - timedelta(days=1)
        
        # This would be similar to collect_current_regulations but with date filter
        # Implementation would search for documents published since since_date
        return []
    
    async def get_document_by_id(self, document_id: str) -> Optional[RegulatoryDocument]:
        """Get specific Federal Register document by document number"""
        try:
            success, error, data = await self._make_request(
                f"{self.base_url}documents/{document_id}.json"
            )
            
            if success and data:
                return await self._create_federal_register_document(data)
                
        except Exception as e:
            logger.error(f"Error getting Federal Register document {document_id}: {str(e)}")
        
        return None
