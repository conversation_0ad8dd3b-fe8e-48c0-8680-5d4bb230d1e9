"""
Regulatory Data Ingestion Orchestrator
Coordinates collection from all regulatory sources and processes the data
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .us_collectors import ECFRCollector, FederalRegisterCollector
from .eu_uk_collectors import EURLexCollector, UKLegislationCollector
from .canada_collectors import JusticeLawsCollector, CanadaGazetteCollector
from .content_processor import ContentProcessor
from .base_collector import BaseRegulatoryCollector, CollectionResult, RegulatoryDocument

logger = logging.getLogger(__name__)


@dataclass
class IngestionResult:
    """Result of a complete ingestion run"""
    success: bool
    total_documents_collected: int
    total_documents_processed: int
    new_documents: int
    updated_documents: int
    sources_processed: int
    sources_failed: int
    errors: List[str]
    duration_seconds: float
    start_time: datetime
    end_time: datetime


class RegulatoryIngestionOrchestrator:
    """Orchestrates regulatory data collection from all sources"""
    
    def __init__(self):
        self.collectors = {
            'us_ecfr': ECFRCollector,
            'us_federal_register': FederalRegisterCollector,
            'eu_eurlex': EURLexCollector,
            'uk_legislation': UKLegislationCollector,
            'ca_justice_laws': JusticeLawsCollector,
            'ca_gazette': CanadaGazetteCollector
        }
        
        # Collection schedules (in hours)
        self.collection_schedules = {
            'us_ecfr': 24,           # Daily - CFR doesn't change frequently
            'us_federal_register': 6, # Every 6 hours - new rules published daily
            'eu_eurlex': 24,         # Daily - EU regulations
            'uk_legislation': 24,    # Daily - UK legislation
            'ca_justice_laws': 24,   # Daily - Canadian laws
            'ca_gazette': 12         # Twice daily - gazette updates
        }
        
        # Priority order for collection (high priority first)
        self.collection_priority = [
            'us_federal_register',   # Most frequent updates
            'ca_gazette',           # Frequent updates
            'us_ecfr',              # Core regulations
            'ca_justice_laws',      # Core Canadian laws
            'eu_eurlex',            # EU regulations
            'uk_legislation'        # UK legislation
        ]
    
    async def run_full_ingestion(self, sources: Optional[List[str]] = None) -> IngestionResult:
        """Run complete ingestion from all or specified sources"""
        start_time = datetime.now()
        
        # Use all sources if none specified
        if sources is None:
            sources = list(self.collectors.keys())
        
        # Sort by priority
        sources = [s for s in self.collection_priority if s in sources]
        
        logger.info(f"Starting full ingestion for {len(sources)} sources: {sources}")
        
        all_documents = []
        collection_results = {}
        errors = []
        sources_failed = 0
        
        # Collect from each source
        for source_name in sources:
            try:
                logger.info(f"Collecting from {source_name}")
                
                collector_class = self.collectors[source_name]
                async with collector_class() as collector:
                    result = await collector.collect_current_regulations()
                    collection_results[source_name] = result
                    
                    if result.success:
                        # Get the documents (this would need to be added to collectors)
                        documents = await self._get_collected_documents(collector, result)
                        all_documents.extend(documents)
                        logger.info(f"Collected {len(documents)} documents from {source_name}")
                    else:
                        sources_failed += 1
                        errors.extend([f"{source_name}: {error}" for error in result.errors])
                        logger.error(f"Failed to collect from {source_name}: {result.errors}")
                
                # Add delay between sources to be respectful
                await asyncio.sleep(5)
                
            except Exception as e:
                sources_failed += 1
                error_msg = f"Error collecting from {source_name}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        # Process all collected documents
        processing_result = {'processed': 0, 'new': 0, 'updated': 0, 'errors': []}
        
        if all_documents:
            try:
                logger.info(f"Processing {len(all_documents)} collected documents")
                
                async with ContentProcessor() as processor:
                    processing_result = await processor.process_documents(all_documents)
                    
                logger.info(f"Processing complete: {processing_result}")
                
            except Exception as e:
                error_msg = f"Error processing documents: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Create final result
        result = IngestionResult(
            success=sources_failed == 0 and len(processing_result.get('errors', [])) == 0,
            total_documents_collected=len(all_documents),
            total_documents_processed=processing_result.get('processed', 0),
            new_documents=processing_result.get('new', 0),
            updated_documents=processing_result.get('updated', 0),
            sources_processed=len(sources) - sources_failed,
            sources_failed=sources_failed,
            errors=errors + processing_result.get('errors', []),
            duration_seconds=duration,
            start_time=start_time,
            end_time=end_time
        )
        
        logger.info(f"Ingestion complete: {result}")
        return result
    
    async def run_incremental_ingestion(self, since_hours: int = 24) -> IngestionResult:
        """Run incremental ingestion for recent changes"""
        start_time = datetime.now()
        since_date = start_time - timedelta(hours=since_hours)
        
        logger.info(f"Starting incremental ingestion since {since_date}")
        
        all_documents = []
        errors = []
        sources_failed = 0
        
        # Check each source for changes
        for source_name in self.collection_priority:
            try:
                collector_class = self.collectors[source_name]
                async with collector_class() as collector:
                    # Detect changes since the specified date
                    changes = await collector.detect_changes(since_date)
                    
                    if changes:
                        all_documents.extend(changes)
                        logger.info(f"Found {len(changes)} changes in {source_name}")
                    else:
                        logger.info(f"No changes found in {source_name}")
                
                await asyncio.sleep(2)  # Shorter delay for incremental
                
            except Exception as e:
                sources_failed += 1
                error_msg = f"Error checking changes in {source_name}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        # Process changes
        processing_result = {'processed': 0, 'new': 0, 'updated': 0, 'errors': []}
        
        if all_documents:
            try:
                async with ContentProcessor() as processor:
                    processing_result = await processor.process_documents(all_documents)
                    
            except Exception as e:
                error_msg = f"Error processing incremental changes: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return IngestionResult(
            success=sources_failed == 0 and len(processing_result.get('errors', [])) == 0,
            total_documents_collected=len(all_documents),
            total_documents_processed=processing_result.get('processed', 0),
            new_documents=processing_result.get('new', 0),
            updated_documents=processing_result.get('updated', 0),
            sources_processed=len(self.collectors) - sources_failed,
            sources_failed=sources_failed,
            errors=errors + processing_result.get('errors', []),
            duration_seconds=duration,
            start_time=start_time,
            end_time=end_time
        )
    
    async def collect_specific_document(self, source_name: str, document_id: str) -> Optional[RegulatoryDocument]:
        """Collect a specific document by ID from a source"""
        try:
            if source_name not in self.collectors:
                logger.error(f"Unknown source: {source_name}")
                return None
            
            collector_class = self.collectors[source_name]
            async with collector_class() as collector:
                document = await collector.get_document_by_id(document_id)
                
                if document:
                    # Process the single document
                    async with ContentProcessor() as processor:
                        await processor.process_documents([document])
                    
                    logger.info(f"Successfully collected and processed document {document_id} from {source_name}")
                    return document
                else:
                    logger.warning(f"Document {document_id} not found in {source_name}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error collecting specific document {document_id} from {source_name}: {str(e)}")
            return None
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """Get status of all collection sources"""
        status = {
            'sources': {},
            'last_run': None,
            'next_scheduled_run': None,
            'total_sources': len(self.collectors),
            'active_sources': 0
        }
        
        try:
            # This would query the database for last collection times
            # For now, return basic status
            for source_name in self.collectors.keys():
                status['sources'][source_name] = {
                    'name': source_name,
                    'status': 'active',
                    'last_collection': None,
                    'next_collection': None,
                    'documents_collected': 0,
                    'success_rate': 100.0
                }
                status['active_sources'] += 1
                
        except Exception as e:
            logger.error(f"Error getting collection status: {str(e)}")
        
        return status
    
    async def _get_collected_documents(self, collector: BaseRegulatoryCollector, 
                                     result: CollectionResult) -> List[RegulatoryDocument]:
        """Get the actual documents from a collection result"""
        # This is a placeholder - in a real implementation, the collectors would
        # need to store and return the actual documents collected
        # For now, return empty list
        return []
    
    def get_recommended_schedule(self) -> Dict[str, str]:
        """Get recommended collection schedule for each source"""
        schedule = {}
        
        for source_name, hours in self.collection_schedules.items():
            if hours < 24:
                schedule[source_name] = f"Every {hours} hours"
            else:
                days = hours // 24
                schedule[source_name] = f"Every {days} day{'s' if days > 1 else ''}"
        
        return schedule


# Convenience functions for common operations
async def run_daily_collection():
    """Run daily collection from all sources"""
    orchestrator = RegulatoryIngestionOrchestrator()
    return await orchestrator.run_full_ingestion()


async def run_hourly_updates():
    """Run hourly updates for frequently changing sources"""
    orchestrator = RegulatoryIngestionOrchestrator()
    frequent_sources = ['us_federal_register', 'ca_gazette']
    return await orchestrator.run_full_ingestion(sources=frequent_sources)


async def check_recent_changes(hours: int = 6):
    """Check for recent changes across all sources"""
    orchestrator = RegulatoryIngestionOrchestrator()
    return await orchestrator.run_incremental_ingestion(since_hours=hours)
