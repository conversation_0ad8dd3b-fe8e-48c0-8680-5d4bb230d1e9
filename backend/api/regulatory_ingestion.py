"""
API endpoints for regulatory data ingestion system
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from pydantic import BaseModel, Field

from services.regulatory_ingestion.orchestrator import (
    RegulatoryIngestionOrchestrator, 
    run_daily_collection,
    run_hourly_updates,
    check_recent_changes
)
from services.ai_chat.regulatory_knowledge_base import (
    get_regulatory_context_for_chat,
    search_regulations_for_chat
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/regulatory-ingestion", tags=["regulatory-ingestion"])


# Request/Response Models
class IngestionRequest(BaseModel):
    sources: Optional[List[str]] = Field(None, description="Specific sources to collect from")
    full_collection: bool = Field(True, description="Whether to run full or incremental collection")
    since_hours: Optional[int] = Field(24, description="Hours to look back for incremental collection")


class IngestionResponse(BaseModel):
    success: bool
    message: str
    job_id: Optional[str] = None
    total_documents_collected: int = 0
    total_documents_processed: int = 0
    new_documents: int = 0
    updated_documents: int = 0
    sources_processed: int = 0
    sources_failed: int = 0
    errors: List[str] = []
    duration_seconds: float = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class SourceStatusResponse(BaseModel):
    sources: Dict[str, Any]
    last_run: Optional[datetime]
    next_scheduled_run: Optional[datetime]
    total_sources: int
    active_sources: int


class RegulatorySearchRequest(BaseModel):
    query: str = Field(..., description="Search query for regulations")
    limit: int = Field(5, description="Maximum number of results to return")
    user_context: Optional[Dict[str, Any]] = Field(None, description="User context for personalized results")


class RegulatorySearchResponse(BaseModel):
    context: str
    regulations: List[Dict[str, Any]]
    confidence: float


# Background task storage (in production, use Redis or database)
background_jobs = {}


@router.post("/collect", response_model=IngestionResponse)
async def start_collection(
    request: IngestionRequest,
    background_tasks: BackgroundTasks
):
    """Start regulatory data collection"""
    try:
        job_id = f"collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Add background task
        if request.full_collection:
            background_tasks.add_task(
                run_collection_job,
                job_id,
                request.sources,
                "full"
            )
            message = f"Started full collection job {job_id}"
        else:
            background_tasks.add_task(
                run_collection_job,
                job_id,
                request.sources,
                "incremental",
                request.since_hours
            )
            message = f"Started incremental collection job {job_id}"
        
        # Store job info
        background_jobs[job_id] = {
            'status': 'running',
            'start_time': datetime.now(),
            'type': 'full' if request.full_collection else 'incremental',
            'sources': request.sources
        }
        
        return IngestionResponse(
            success=True,
            message=message,
            job_id=job_id
        )
        
    except Exception as e:
        logger.error(f"Error starting collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=SourceStatusResponse)
async def get_collection_status():
    """Get status of all collection sources"""
    try:
        orchestrator = RegulatoryIngestionOrchestrator()
        status = await orchestrator.get_collection_status()
        
        return SourceStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Error getting collection status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/job/{job_id}", response_model=IngestionResponse)
async def get_job_status(job_id: str):
    """Get status of a specific collection job"""
    try:
        if job_id not in background_jobs:
            raise HTTPException(status_code=404, detail="Job not found")
        
        job_info = background_jobs[job_id]
        
        return IngestionResponse(
            success=job_info['status'] == 'completed',
            message=f"Job {job_id} is {job_info['status']}",
            job_id=job_id,
            **job_info.get('result', {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/collect/daily")
async def trigger_daily_collection(background_tasks: BackgroundTasks):
    """Trigger daily collection from all sources"""
    try:
        job_id = f"daily_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        background_tasks.add_task(run_daily_collection_job, job_id)
        
        background_jobs[job_id] = {
            'status': 'running',
            'start_time': datetime.now(),
            'type': 'daily',
            'sources': None
        }
        
        return {
            "success": True,
            "message": f"Started daily collection job {job_id}",
            "job_id": job_id
        }
        
    except Exception as e:
        logger.error(f"Error starting daily collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/collect/hourly")
async def trigger_hourly_updates(background_tasks: BackgroundTasks):
    """Trigger hourly updates for frequently changing sources"""
    try:
        job_id = f"hourly_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        background_tasks.add_task(run_hourly_updates_job, job_id)
        
        background_jobs[job_id] = {
            'status': 'running',
            'start_time': datetime.now(),
            'type': 'hourly',
            'sources': ['us_federal_register', 'ca_gazette']
        }
        
        return {
            "success": True,
            "message": f"Started hourly updates job {job_id}",
            "job_id": job_id
        }
        
    except Exception as e:
        logger.error(f"Error starting hourly updates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sources")
async def get_available_sources():
    """Get list of available regulatory sources"""
    try:
        orchestrator = RegulatoryIngestionOrchestrator()
        
        sources = {
            'us_ecfr': {
                'name': 'Electronic Code of Federal Regulations',
                'region': 'US',
                'description': 'Current CFR titles including 21 CFR (FDA) and 9 CFR (USDA-FSIS)',
                'update_frequency': 'Daily'
            },
            'us_federal_register': {
                'name': 'Federal Register',
                'region': 'US',
                'description': 'Final and proposed rules with effective dates',
                'update_frequency': 'Every 6 hours'
            },
            'eu_eurlex': {
                'name': 'EUR-Lex',
                'region': 'EU',
                'description': 'EU regulations, directives, and EFSA opinions',
                'update_frequency': 'Daily'
            },
            'uk_legislation': {
                'name': 'UK Legislation',
                'region': 'UK',
                'description': 'UK and devolved-region food regulations plus retained EU law',
                'update_frequency': 'Daily'
            },
            'ca_justice_laws': {
                'name': 'Justice Laws Canada',
                'region': 'CA',
                'description': 'Federal Acts and regulations including SFCR and Food & Drugs Act',
                'update_frequency': 'Daily'
            },
            'ca_gazette': {
                'name': 'Canada Gazette',
                'region': 'CA',
                'description': 'Draft and final amendments before they appear in Justice Laws',
                'update_frequency': 'Every 12 hours'
            }
        }
        
        schedule = orchestrator.get_recommended_schedule()
        
        return {
            'sources': sources,
            'recommended_schedule': schedule,
            'total_sources': len(sources)
        }
        
    except Exception as e:
        logger.error(f"Error getting available sources: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", response_model=RegulatorySearchResponse)
async def search_regulations(request: RegulatorySearchRequest):
    """Search regulations for AI chat responses"""
    try:
        # Get regulatory context
        context = await get_regulatory_context_for_chat(
            request.query, 
            request.user_context
        )
        
        # Get simplified regulation results
        regulations = await search_regulations_for_chat(
            request.query,
            request.limit
        )
        
        # Calculate confidence (simplified)
        confidence = 0.8 if regulations else 0.3
        
        return RegulatorySearchResponse(
            context=context,
            regulations=regulations,
            confidence=confidence
        )
        
    except Exception as e:
        logger.error(f"Error searching regulations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recent-updates")
async def get_recent_updates(
    days: int = Query(30, description="Number of days to look back"),
    region: Optional[str] = Query(None, description="Filter by region (US, EU, UK, CA)")
):
    """Get recent regulatory updates"""
    try:
        from services.ai_chat.regulatory_knowledge_base import RegulatoryKnowledgeBase
        
        async with RegulatoryKnowledgeBase() as kb:
            updates = await kb.get_recent_updates(days, region)
        
        return {
            'updates': updates,
            'total_count': len(updates),
            'time_period': f"Last {days} days",
            'region_filter': region
        }
        
    except Exception as e:
        logger.error(f"Error getting recent updates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Background task functions
async def run_collection_job(job_id: str, sources: Optional[List[str]], 
                           collection_type: str, since_hours: int = 24):
    """Run collection job in background"""
    try:
        orchestrator = RegulatoryIngestionOrchestrator()
        
        if collection_type == "full":
            result = await orchestrator.run_full_ingestion(sources)
        else:
            result = await orchestrator.run_incremental_ingestion(since_hours)
        
        # Update job status
        background_jobs[job_id].update({
            'status': 'completed' if result.success else 'failed',
            'end_time': datetime.now(),
            'result': {
                'total_documents_collected': result.total_documents_collected,
                'total_documents_processed': result.total_documents_processed,
                'new_documents': result.new_documents,
                'updated_documents': result.updated_documents,
                'sources_processed': result.sources_processed,
                'sources_failed': result.sources_failed,
                'errors': result.errors,
                'duration_seconds': result.duration_seconds
            }
        })
        
        logger.info(f"Collection job {job_id} completed: {result}")
        
    except Exception as e:
        logger.error(f"Error in collection job {job_id}: {str(e)}")
        background_jobs[job_id].update({
            'status': 'failed',
            'end_time': datetime.now(),
            'error': str(e)
        })


async def run_daily_collection_job(job_id: str):
    """Run daily collection job"""
    try:
        result = await run_daily_collection()
        
        background_jobs[job_id].update({
            'status': 'completed' if result.success else 'failed',
            'end_time': datetime.now(),
            'result': {
                'total_documents_collected': result.total_documents_collected,
                'total_documents_processed': result.total_documents_processed,
                'new_documents': result.new_documents,
                'updated_documents': result.updated_documents,
                'sources_processed': result.sources_processed,
                'sources_failed': result.sources_failed,
                'errors': result.errors,
                'duration_seconds': result.duration_seconds
            }
        })
        
    except Exception as e:
        logger.error(f"Error in daily collection job {job_id}: {str(e)}")
        background_jobs[job_id].update({
            'status': 'failed',
            'end_time': datetime.now(),
            'error': str(e)
        })


async def run_hourly_updates_job(job_id: str):
    """Run hourly updates job"""
    try:
        result = await run_hourly_updates()
        
        background_jobs[job_id].update({
            'status': 'completed' if result.success else 'failed',
            'end_time': datetime.now(),
            'result': {
                'total_documents_collected': result.total_documents_collected,
                'total_documents_processed': result.total_documents_processed,
                'new_documents': result.new_documents,
                'updated_documents': result.updated_documents,
                'sources_processed': result.sources_processed,
                'sources_failed': result.sources_failed,
                'errors': result.errors,
                'duration_seconds': result.duration_seconds
            }
        })
        
    except Exception as e:
        logger.error(f"Error in hourly updates job {job_id}: {str(e)}")
        background_jobs[job_id].update({
            'status': 'failed',
            'end_time': datetime.now(),
            'error': str(e)
        })
