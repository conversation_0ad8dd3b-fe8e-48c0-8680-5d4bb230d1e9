"""
GraphQL Types for FoodRegs Platform

Defines all GraphQL types, enums, and input types for the regulatory intelligence API.
"""
import strawberry
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


# Enums
@strawberry.enum
class RegionType(Enum):
    US = "US"
    EU = "EU"
    CA = "CA"
    UK = "UK"
    CN = "CN"
    GLOBAL = "GLOBAL"


@strawberry.enum
class DocumentType(Enum):
    NOTICE = "Notice"
    DRAFT_RULE = "DraftRule"
    FINAL_RULE = "FinalRule"
    MEMO = "Memo"
    GUIDANCE = "Guidance"
    OTHER = "Other"


@strawberry.enum
class SourceType(Enum):
    RSS = "RSS"
    EMAIL = "EMAIL"
    SCRAPER = "SCRAPER"
    API = "API"


@strawberry.enum
class CollectionStatus(Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    RETRYING = "RETRYING"


@strawberry.enum
class ActionStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    DISMISSED = "dismissed"


@strawberry.enum
class SortOrder(Enum):
    ASC = "ASC"
    DESC = "DESC"


# Scalar types
@strawberry.scalar
class JSON:
    """JSON scalar type for flexible metadata storage"""
    serialize = lambda v: v
    parse_value = lambda v: v


# Core Types
@strawberry.type
class RegulatorySource:
    """Regulatory data source configuration"""
    id: strawberry.ID
    source_id: str
    name: str
    agency: str
    region: RegionType
    source_type: SourceType
    url: Optional[str]
    config: JSON
    schedule_cron: str
    requests_per_minute: int
    timeout_seconds: int
    is_active: bool
    last_collection_at: Optional[datetime]
    last_success_at: Optional[datetime]
    consecutive_failures: int
    created_at: datetime
    updated_at: datetime


@strawberry.type
class Regulation:
    """Core regulation/document type"""
    id: strawberry.ID
    record_id: strawberry.ID
    version: int
    source_id: strawberry.ID
    url: str
    title: str
    pub_date: datetime
    ingested_at: datetime
    region: RegionType
    doc_type: DocumentType
    language: str
    summary: Optional[str]
    full_text: str
    raw_uri: str
    hash: str
    tags: JSON
    is_latest_version: bool
    previous_version_id: Optional[strawberry.ID]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    word_count: int
    text_length: int

    # Intent classification fields
    intent_category: Optional[str]
    intent_confidence: Optional[float]
    intent_reasoning: Optional[str]
    intent_secondary: Optional[JSON]
    intent_keywords: Optional[JSON]

    # Relationships (will be resolved by resolvers)
    source: Optional[RegulatorySource]
    actions: List["Action"]
    previous_version: Optional["Regulation"]


@strawberry.type
class Action:
    """AI-generated compliance action"""
    id: strawberry.ID
    regulation_id: strawberry.ID
    description: str
    priority: int  # 1-critical to 5-low
    due_date: Optional[datetime]
    confidence: float  # 0.0 to 1.0
    explanation: Optional[str]
    client_id: Optional[strawberry.ID]
    status: ActionStatus
    model_version: Optional[str]
    prompt_version: Optional[str]
    generation_cost: Optional[float]
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    regulation: Optional[Regulation]


@strawberry.type
class CollectionJob:
    """Data collection job execution record"""
    id: strawberry.ID
    source_id: strawberry.ID
    status: CollectionStatus
    started_at: datetime
    completed_at: Optional[datetime]
    documents_found: int
    documents_new: int
    documents_updated: int
    documents_failed: int
    error_message: Optional[str]
    retry_count: int
    duration_seconds: Optional[float]
    bytes_processed: int
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    source: Optional[RegulatorySource]


@strawberry.type
class SystemMetric:
    """System-wide metrics and health indicators"""
    id: strawberry.ID
    timestamp: datetime
    metric_name: str
    metric_type: str
    value: float
    labels: JSON


# Aggregation and Statistics Types
@strawberry.type
class RegulationStats:
    """Statistics about regulations"""
    total_count: int
    by_region: List["RegionCount"]
    by_document_type: List["DocumentTypeCount"]
    by_language: List["LanguageCount"]
    by_source: List["SourceCount"]
    recent_count_24h: int
    recent_count_7d: int
    recent_count_30d: int


@strawberry.type
class RegionCount:
    """Count by region"""
    region: RegionType
    count: int


@strawberry.type
class DocumentTypeCount:
    """Count by document type"""
    doc_type: DocumentType
    count: int


@strawberry.type
class LanguageCount:
    """Count by language"""
    language: str
    count: int


@strawberry.type
class SourceCount:
    """Count by source"""
    source_id: str
    source_name: str
    count: int


@strawberry.type
class CollectionStats:
    """Collection job statistics"""
    total_jobs: int
    successful_jobs: int
    failed_jobs: int
    success_rate: float
    avg_duration_seconds: float
    total_documents_collected: int
    active_sources: int
    failing_sources: int


@strawberry.type
class SearchResult:
    """Search result with relevance scoring"""
    regulation: Regulation
    relevance_score: float
    matched_fields: List[str]
    highlights: JSON


@strawberry.type
class SearchResponse:
    """Paginated search response"""
    results: List[SearchResult]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    query: str
    execution_time_ms: float


# Input Types
@strawberry.input
class RegulationFilter:
    """Filter criteria for regulations"""
    regions: Optional[List[RegionType]] = None
    doc_types: Optional[List[DocumentType]] = None
    languages: Optional[List[str]] = None
    source_ids: Optional[List[str]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    is_latest_version: Optional[bool] = None
    has_actions: Optional[bool] = None
    min_word_count: Optional[int] = None
    max_word_count: Optional[int] = None


@strawberry.input
class RegulationSort:
    """Sort criteria for regulations"""
    field: str  # pub_date, ingested_at, title, word_count
    order: SortOrder = SortOrder.DESC


@strawberry.input
class PaginationInput:
    """Pagination parameters"""
    page: int = 1
    page_size: int = 20


@strawberry.input
class SearchInput:
    """Search parameters"""
    query: str
    filters: Optional[RegulationFilter] = None
    sort: Optional[RegulationSort] = None
    pagination: Optional[PaginationInput] = None
    highlight: bool = True
    include_similar: bool = False


@strawberry.input
class ActionFilter:
    """Filter criteria for actions"""
    regulation_ids: Optional[List[strawberry.ID]] = None
    priorities: Optional[List[int]] = None
    statuses: Optional[List[ActionStatus]] = None
    client_id: Optional[strawberry.ID] = None
    due_date_from: Optional[datetime] = None
    due_date_to: Optional[datetime] = None
    min_confidence: Optional[float] = None


@strawberry.input
class CollectionJobFilter:
    """Filter criteria for collection jobs"""
    source_ids: Optional[List[strawberry.ID]] = None
    statuses: Optional[List[CollectionStatus]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


@strawberry.input
class CreateActionInput:
    """Input for creating a new action"""
    regulation_id: strawberry.ID
    description: str
    priority: int
    due_date: Optional[datetime] = None
    client_id: Optional[strawberry.ID] = None


@strawberry.input
class UpdateActionInput:
    """Input for updating an action"""
    id: strawberry.ID
    description: Optional[str] = None
    priority: Optional[int] = None
    due_date: Optional[datetime] = None
    status: Optional[ActionStatus] = None


@strawberry.input
class CreateSourceInput:
    """Input for creating a new regulatory source"""
    source_id: str
    name: str
    agency: str
    region: RegionType
    source_type: SourceType
    url: Optional[str] = None
    config: Optional[JSON] = None
    schedule_cron: str = "0 */30 * * *"
    requests_per_minute: int = 4
    timeout_seconds: int = 30


@strawberry.input
class UpdateSourceInput:
    """Input for updating a regulatory source"""
    id: strawberry.ID
    name: Optional[str] = None
    url: Optional[str] = None
    config: Optional[JSON] = None
    schedule_cron: Optional[str] = None
    requests_per_minute: Optional[int] = None
    timeout_seconds: Optional[int] = None
    is_active: Optional[bool] = None


# Response Types
@strawberry.type
class RegulationResponse:
    """Paginated regulation response"""
    regulations: List[Regulation]
    total_count: int
    page: int
    page_size: int
    total_pages: int


@strawberry.type
class ActionResponse:
    """Paginated action response"""
    actions: List[Action]
    total_count: int
    page: int
    page_size: int
    total_pages: int


@strawberry.type
class CollectionJobResponse:
    """Paginated collection job response"""
    jobs: List[CollectionJob]
    total_count: int
    page: int
    page_size: int
    total_pages: int


@strawberry.type
class MutationResult:
    """Generic mutation result"""
    success: bool
    message: str
    errors: Optional[List[str]] = None


@strawberry.type
class CreateActionResult(MutationResult):
    """Result of creating an action"""
    action: Optional[Action] = None


@strawberry.type
class UpdateActionResult(MutationResult):
    """Result of updating an action"""
    action: Optional[Action] = None


@strawberry.type
class CreateSourceResult(MutationResult):
    """Result of creating a source"""
    source: Optional[RegulatorySource] = None


@strawberry.type
class UpdateSourceResult(MutationResult):
    """Result of updating a source"""
    source: Optional[RegulatorySource] = None


@strawberry.type
class TriggerCollectionResult(MutationResult):
    """Result of triggering a collection"""
    job: Optional[CollectionJob] = None


# Dashboard Types
@strawberry.type
class SourceMetrics:
    """Detailed metrics for a regulatory source"""
    source_id: str
    name: str
    agency: str
    region: RegionType
    is_active: bool

    # Collection metrics
    total_documents: int
    documents_24h: int
    documents_7d: int
    documents_30d: int

    # Health metrics
    last_collection_at: Optional[datetime]
    last_success_at: Optional[datetime]
    consecutive_failures: int
    success_rate_7d: float

    # Content metrics
    avg_document_length: float
    primary_language: str
    document_types: JSON
    intent_distribution: JSON


@strawberry.type
class IntentMetrics:
    """Metrics for a regulatory intent category"""
    intent: str
    total_documents: int
    documents_24h: int
    documents_7d: int
    documents_30d: int

    # Breakdown data
    top_sources: JSON
    regional_distribution: JSON
    daily_counts: JSON

    # Quality metrics
    avg_confidence: float
    high_confidence_count: int


@strawberry.type
class DashboardSummary:
    """Overall dashboard summary metrics"""
    # Overall metrics
    total_sources: int
    active_sources: int
    total_documents: int
    documents_24h: int
    documents_7d: int

    # Health metrics
    healthy_sources: int
    failing_sources: int
    overall_success_rate: float

    # Content metrics
    total_actions: int
    pending_actions: int
    languages_detected: int

    # Recent activity
    recent_collections: int
    avg_processing_time: float


@strawberry.type
class TrendingTopic:
    """Trending regulatory topic"""
    topic: str
    mentions: int
    trend: str  # "up", "down", "stable"
    change_percent: Optional[float] = None


@strawberry.type
class IntentClassificationResult:
    """Result of intent classification"""
    intent: str
    confidence: float
    reasoning: str
    secondary_intents: JSON


@strawberry.type
class ClassificationStats:
    """Statistics from document classification"""
    total_classified: int
    average_confidence: float
    intent_distribution: JSON
    high_confidence_count: int
    low_confidence_count: int
    sample_classifications: List[IntentClassificationResult]


@strawberry.type
class DashboardData:
    """Complete dashboard data"""
    summary: DashboardSummary
    source_metrics: List[SourceMetrics]
    intent_metrics: List[IntentMetrics]
    trending_topics: List[TrendingTopic]
    classification_stats: Optional[ClassificationStats] = None
