"""
GraphQL Resolvers for FoodRegs Platform

Implements all GraphQL query and mutation resolvers for the regulatory intelligence API.
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import math

import strawberry
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from database.connection import get_async_session, AsyncSessionLocal
from database.models import (
    RegulatorySource as DBRegulatorySource,
    Regulation as DBRegulation,
    Action as DBAction,
    CollectionJob as DBCollectionJob,
    SystemMetric as DBSystemMetric
)
from api.graphql.types import (
    RegulatorySource, Regulation, Action, CollectionJob, SystemMetric,
    RegulationResponse, ActionResponse, CollectionJobResponse,
    RegulationStats, CollectionStats, SearchResponse,
    RegulationFilter, ActionFilter, CollectionJobFilter,
    PaginationInput, RegulationSort, SearchInput,
    CreateActionInput, UpdateActionInput, CreateSourceInput, UpdateSourceInput,
    CreateActionResult, UpdateActionResult, CreateSourceResult, UpdateSourceResult,
    TriggerCollectionResult, SortOrder,
    # Dashboard types
    DashboardSummary, SourceMetrics, IntentMetrics, TrendingTopic,
    IntentClassificationResult, ClassificationStats, DashboardData
)
from services.collector_service import collector_service


logger = logging.getLogger(__name__)


class RegulationResolver:
    """Resolvers for regulation-related queries"""
    
    @staticmethod
    async def get_regulations(
        filters: Optional[RegulationFilter] = None,
        sort: Optional[RegulationSort] = None,
        pagination: Optional[PaginationInput] = None
    ) -> RegulationResponse:
        """Get paginated list of regulations with filtering and sorting"""
        
        async with AsyncSessionLocal() as session:
            # Build base query
            query = select(DBRegulation).options(
                selectinload(DBRegulation.source),
                selectinload(DBRegulation.actions)
            )
            
            # Apply filters
            if filters:
                query = RegulationResolver._apply_filters(query, filters)
            
            # Apply sorting
            if sort:
                query = RegulationResolver._apply_sorting(query, sort)
            else:
                # Default sort by publication date (newest first)
                query = query.order_by(desc(DBRegulation.pub_date))
            
            # Get total count for pagination
            count_query = select(func.count(DBRegulation.id))
            if filters:
                count_query = RegulationResolver._apply_filters(count_query, filters)
            
            total_count_result = await session.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply pagination
            page = pagination.page if pagination else 1
            page_size = pagination.page_size if pagination else 20
            offset = (page - 1) * page_size
            
            query = query.offset(offset).limit(page_size)
            
            # Execute query
            result = await session.execute(query)
            db_regulations = result.scalars().all()
            
            # Convert to GraphQL types
            regulations = [
                RegulationResolver._db_to_graphql(reg) for reg in db_regulations
            ]
            
            total_pages = math.ceil(total_count / page_size)
            
            return RegulationResponse(
                regulations=regulations,
                total_count=total_count,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
    
    @staticmethod
    async def get_regulation_by_id(regulation_id: strawberry.ID) -> Optional[Regulation]:
        """Get a specific regulation by ID"""
        
        async with get_async_session() as session:
            query = select(DBRegulation).options(
                selectinload(DBRegulation.source),
                selectinload(DBRegulation.actions),
                selectinload(DBRegulation.previous_version)
            ).where(DBRegulation.id == regulation_id)
            
            result = await session.execute(query)
            db_regulation = result.scalar_one_or_none()
            
            if db_regulation:
                return RegulationResolver._db_to_graphql(db_regulation)
            return None
    
    @staticmethod
    async def get_regulation_stats() -> RegulationStats:
        """Get regulation statistics"""
        
        async with get_async_session() as session:
            # Total count
            total_result = await session.execute(select(func.count(DBRegulation.id)))
            total_count = total_result.scalar()
            
            # Recent counts
            now = datetime.now(timezone.utc)
            
            # 24 hours
            recent_24h_result = await session.execute(
                select(func.count(DBRegulation.id)).where(
                    DBRegulation.ingested_at >= now - func.interval('24 hours')
                )
            )
            recent_count_24h = recent_24h_result.scalar()
            
            # 7 days
            recent_7d_result = await session.execute(
                select(func.count(DBRegulation.id)).where(
                    DBRegulation.ingested_at >= now - func.interval('7 days')
                )
            )
            recent_count_7d = recent_7d_result.scalar()
            
            # 30 days
            recent_30d_result = await session.execute(
                select(func.count(DBRegulation.id)).where(
                    DBRegulation.ingested_at >= now - func.interval('30 days')
                )
            )
            recent_count_30d = recent_30d_result.scalar()
            
            # Counts by region
            region_result = await session.execute(
                select(DBRegulation.region, func.count(DBRegulation.id))
                .group_by(DBRegulation.region)
            )
            by_region = [
                {"region": region, "count": count}
                for region, count in region_result.all()
            ]
            
            # Counts by document type
            doc_type_result = await session.execute(
                select(DBRegulation.doc_type, func.count(DBRegulation.id))
                .group_by(DBRegulation.doc_type)
            )
            by_document_type = [
                {"doc_type": doc_type, "count": count}
                for doc_type, count in doc_type_result.all()
            ]
            
            # Counts by language
            language_result = await session.execute(
                select(DBRegulation.language, func.count(DBRegulation.id))
                .group_by(DBRegulation.language)
                .order_by(desc(func.count(DBRegulation.id)))
                .limit(10)  # Top 10 languages
            )
            by_language = [
                {"language": language, "count": count}
                for language, count in language_result.all()
            ]
            
            # Counts by source
            source_result = await session.execute(
                select(
                    DBRegulatorySource.source_id,
                    DBRegulatorySource.name,
                    func.count(DBRegulation.id)
                )
                .join(DBRegulation, DBRegulation.source_id == DBRegulatorySource.id)
                .group_by(DBRegulatorySource.source_id, DBRegulatorySource.name)
                .order_by(desc(func.count(DBRegulation.id)))
            )
            by_source = [
                {"source_id": source_id, "source_name": name, "count": count}
                for source_id, name, count in source_result.all()
            ]
            
            return RegulationStats(
                total_count=total_count,
                by_region=by_region,
                by_document_type=by_document_type,
                by_language=by_language,
                by_source=by_source,
                recent_count_24h=recent_count_24h,
                recent_count_7d=recent_count_7d,
                recent_count_30d=recent_count_30d
            )
    
    @staticmethod
    def _apply_filters(query, filters: RegulationFilter):
        """Apply filters to regulation query"""
        conditions = []
        
        if filters.regions:
            conditions.append(DBRegulation.region.in_([r.value for r in filters.regions]))
        
        if filters.doc_types:
            conditions.append(DBRegulation.doc_type.in_([dt.value for dt in filters.doc_types]))
        
        if filters.languages:
            conditions.append(DBRegulation.language.in_(filters.languages))
        
        if filters.source_ids:
            # Join with source table to filter by source_id
            query = query.join(DBRegulatorySource)
            conditions.append(DBRegulatorySource.source_id.in_(filters.source_ids))
        
        if filters.date_from:
            conditions.append(DBRegulation.pub_date >= filters.date_from)
        
        if filters.date_to:
            conditions.append(DBRegulation.pub_date <= filters.date_to)
        
        if filters.is_latest_version is not None:
            conditions.append(DBRegulation.is_latest_version == filters.is_latest_version)
        
        if filters.has_actions is not None:
            if filters.has_actions:
                query = query.join(DBAction)
            else:
                query = query.outerjoin(DBAction).where(DBAction.id.is_(None))
        
        if filters.min_word_count:
            # Calculate word count from full_text
            conditions.append(
                func.array_length(func.string_to_array(DBRegulation.full_text, ' '), 1) >= filters.min_word_count
            )
        
        if filters.max_word_count:
            conditions.append(
                func.array_length(func.string_to_array(DBRegulation.full_text, ' '), 1) <= filters.max_word_count
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        return query
    
    @staticmethod
    def _apply_sorting(query, sort: RegulationSort):
        """Apply sorting to regulation query"""
        field_mapping = {
            'pub_date': DBRegulation.pub_date,
            'ingested_at': DBRegulation.ingested_at,
            'title': DBRegulation.title,
            'word_count': func.array_length(func.string_to_array(DBRegulation.full_text, ' '), 1)
        }
        
        sort_field = field_mapping.get(sort.field, DBRegulation.pub_date)
        
        if sort.order == SortOrder.ASC:
            query = query.order_by(asc(sort_field))
        else:
            query = query.order_by(desc(sort_field))
        
        return query
    
    @staticmethod
    def _db_to_graphql(db_regulation: DBRegulation) -> Regulation:
        """Convert database model to GraphQL type"""
        return Regulation(
            id=str(db_regulation.id),
            record_id=str(db_regulation.record_id),
            version=db_regulation.version,
            source_id=str(db_regulation.source_id),
            url=db_regulation.url,
            title=db_regulation.title,
            pub_date=db_regulation.pub_date,
            ingested_at=db_regulation.ingested_at,
            region=db_regulation.region,
            doc_type=db_regulation.doc_type,
            language=db_regulation.language,
            summary=db_regulation.summary,
            full_text=db_regulation.full_text,
            raw_uri=db_regulation.raw_uri,
            hash=db_regulation.hash,
            tags=db_regulation.tags,
            is_latest_version=db_regulation.is_latest_version,
            previous_version_id=str(db_regulation.previous_version_id) if db_regulation.previous_version_id else None,
            created_at=db_regulation.created_at,
            updated_at=db_regulation.updated_at,
            word_count=len(db_regulation.full_text.split()) if db_regulation.full_text else 0,
            text_length=len(db_regulation.full_text) if db_regulation.full_text else 0,
            intent_category=db_regulation.intent_category,
            intent_confidence=db_regulation.intent_confidence,
            intent_reasoning=db_regulation.intent_reasoning,
            intent_secondary=db_regulation.intent_secondary,
            intent_keywords=db_regulation.intent_keywords,
            source=SourceResolver._db_to_graphql(db_regulation.source) if db_regulation.source else None,
            actions=[ActionResolver._db_to_graphql(action) for action in db_regulation.actions] if db_regulation.actions else [],
            previous_version=RegulationResolver._db_to_graphql(db_regulation.previous_version) if db_regulation.previous_version else None
        )


class SourceResolver:
    """Resolvers for regulatory source queries"""
    
    @staticmethod
    async def get_sources(active_only: bool = True) -> List[RegulatorySource]:
        """Get list of regulatory sources"""
        
        async with get_async_session() as session:
            query = select(DBRegulatorySource)
            
            if active_only:
                query = query.where(DBRegulatorySource.is_active == True)
            
            query = query.order_by(DBRegulatorySource.name)
            
            result = await session.execute(query)
            db_sources = result.scalars().all()
            
            return [SourceResolver._db_to_graphql(source) for source in db_sources]
    
    @staticmethod
    async def get_source_by_id(source_id: strawberry.ID) -> Optional[RegulatorySource]:
        """Get a specific source by ID"""
        
        async with get_async_session() as session:
            query = select(DBRegulatorySource).where(DBRegulatorySource.id == source_id)
            
            result = await session.execute(query)
            db_source = result.scalar_one_or_none()
            
            if db_source:
                return SourceResolver._db_to_graphql(db_source)
            return None
    
    @staticmethod
    def _db_to_graphql(db_source: DBRegulatorySource) -> RegulatorySource:
        """Convert database model to GraphQL type"""
        return RegulatorySource(
            id=str(db_source.id),
            source_id=db_source.source_id,
            name=db_source.name,
            agency=db_source.agency,
            region=db_source.region,
            source_type=db_source.source_type,
            url=db_source.url,
            config=db_source.config,
            schedule_cron=db_source.schedule_cron,
            requests_per_minute=db_source.requests_per_minute,
            timeout_seconds=db_source.timeout_seconds,
            is_active=db_source.is_active,
            last_collection_at=db_source.last_collection_at,
            last_success_at=db_source.last_success_at,
            consecutive_failures=db_source.consecutive_failures,
            created_at=db_source.created_at,
            updated_at=db_source.updated_at
        )


class ActionResolver:
    """Resolvers for action-related queries"""
    
    @staticmethod
    async def get_actions(
        filters: Optional[ActionFilter] = None,
        pagination: Optional[PaginationInput] = None
    ) -> ActionResponse:
        """Get paginated list of actions with filtering"""
        
        async with get_async_session() as session:
            # Build base query
            query = select(DBAction).options(selectinload(DBAction.regulation))
            
            # Apply filters
            if filters:
                query = ActionResolver._apply_filters(query, filters)
            
            # Default sort by priority (highest first), then by creation date
            query = query.order_by(asc(DBAction.priority), desc(DBAction.created_at))
            
            # Get total count
            count_query = select(func.count(DBAction.id))
            if filters:
                count_query = ActionResolver._apply_filters(count_query, filters)
            
            total_count_result = await session.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply pagination
            page = pagination.page if pagination else 1
            page_size = pagination.page_size if pagination else 20
            offset = (page - 1) * page_size
            
            query = query.offset(offset).limit(page_size)
            
            # Execute query
            result = await session.execute(query)
            db_actions = result.scalars().all()
            
            # Convert to GraphQL types
            actions = [ActionResolver._db_to_graphql(action) for action in db_actions]
            
            total_pages = math.ceil(total_count / page_size)
            
            return ActionResponse(
                actions=actions,
                total_count=total_count,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
    
    @staticmethod
    def _apply_filters(query, filters: ActionFilter):
        """Apply filters to action query"""
        conditions = []
        
        if filters.regulation_ids:
            conditions.append(DBAction.regulation_id.in_(filters.regulation_ids))
        
        if filters.priorities:
            conditions.append(DBAction.priority.in_(filters.priorities))
        
        if filters.statuses:
            conditions.append(DBAction.status.in_([s.value for s in filters.statuses]))
        
        if filters.client_id:
            conditions.append(DBAction.client_id == filters.client_id)
        
        if filters.due_date_from:
            conditions.append(DBAction.due_date >= filters.due_date_from)
        
        if filters.due_date_to:
            conditions.append(DBAction.due_date <= filters.due_date_to)
        
        if filters.min_confidence:
            conditions.append(DBAction.confidence >= filters.min_confidence)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        return query
    
    @staticmethod
    def _db_to_graphql(db_action: DBAction) -> Action:
        """Convert database model to GraphQL type"""
        return Action(
            id=str(db_action.id),
            regulation_id=str(db_action.regulation_id),
            description=db_action.description,
            priority=db_action.priority,
            due_date=db_action.due_date,
            confidence=db_action.confidence,
            explanation=db_action.explanation,
            client_id=str(db_action.client_id) if db_action.client_id else None,
            status=db_action.status,
            model_version=db_action.model_version,
            prompt_version=db_action.prompt_version,
            generation_cost=db_action.generation_cost,
            created_at=db_action.created_at,
            updated_at=db_action.updated_at,
            regulation=RegulationResolver._db_to_graphql(db_action.regulation) if db_action.regulation else None
        )


class CollectionJobResolver:
    """Resolvers for collection job queries"""

    @staticmethod
    async def get_collection_jobs(
        filters: Optional[CollectionJobFilter] = None,
        pagination: Optional[PaginationInput] = None
    ) -> CollectionJobResponse:
        """Get paginated list of collection jobs with filtering"""

        async with get_async_session() as session:
            # Build base query
            query = select(DBCollectionJob).options(selectinload(DBCollectionJob.source))

            # Apply filters
            if filters:
                query = CollectionJobResolver._apply_filters(query, filters)

            # Default sort by start time (newest first)
            query = query.order_by(desc(DBCollectionJob.started_at))

            # Get total count
            count_query = select(func.count(DBCollectionJob.id))
            if filters:
                count_query = CollectionJobResolver._apply_filters(count_query, filters)

            total_count_result = await session.execute(count_query)
            total_count = total_count_result.scalar()

            # Apply pagination
            page = pagination.page if pagination else 1
            page_size = pagination.page_size if pagination else 20
            offset = (page - 1) * page_size

            query = query.offset(offset).limit(page_size)

            # Execute query
            result = await session.execute(query)
            db_jobs = result.scalars().all()

            # Convert to GraphQL types
            jobs = [CollectionJobResolver._db_to_graphql(job) for job in db_jobs]

            total_pages = math.ceil(total_count / page_size)

            return CollectionJobResponse(
                jobs=jobs,
                total_count=total_count,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )

    @staticmethod
    async def get_collection_stats() -> CollectionStats:
        """Get collection job statistics"""

        async with get_async_session() as session:
            # Total jobs
            total_result = await session.execute(select(func.count(DBCollectionJob.id)))
            total_jobs = total_result.scalar()

            # Successful jobs
            success_result = await session.execute(
                select(func.count(DBCollectionJob.id)).where(DBCollectionJob.status == 'SUCCESS')
            )
            successful_jobs = success_result.scalar()

            # Failed jobs
            failed_result = await session.execute(
                select(func.count(DBCollectionJob.id)).where(DBCollectionJob.status == 'FAILED')
            )
            failed_jobs = failed_result.scalar()

            # Success rate
            success_rate = successful_jobs / total_jobs if total_jobs > 0 else 0.0

            # Average duration
            avg_duration_result = await session.execute(
                select(func.avg(DBCollectionJob.duration_seconds)).where(
                    DBCollectionJob.duration_seconds.is_not(None)
                )
            )
            avg_duration_seconds = avg_duration_result.scalar() or 0.0

            # Total documents collected
            total_docs_result = await session.execute(
                select(func.sum(DBCollectionJob.documents_new + DBCollectionJob.documents_updated))
            )
            total_documents_collected = total_docs_result.scalar() or 0

            # Active sources
            active_sources_result = await session.execute(
                select(func.count(DBRegulatorySource.id)).where(DBRegulatorySource.is_active == True)
            )
            active_sources = active_sources_result.scalar()

            # Failing sources (3+ consecutive failures)
            failing_sources_result = await session.execute(
                select(func.count(DBRegulatorySource.id)).where(
                    and_(
                        DBRegulatorySource.is_active == True,
                        DBRegulatorySource.consecutive_failures >= 3
                    )
                )
            )
            failing_sources = failing_sources_result.scalar()

            return CollectionStats(
                total_jobs=total_jobs,
                successful_jobs=successful_jobs,
                failed_jobs=failed_jobs,
                success_rate=success_rate,
                avg_duration_seconds=avg_duration_seconds,
                total_documents_collected=total_documents_collected,
                active_sources=active_sources,
                failing_sources=failing_sources
            )

    @staticmethod
    def _apply_filters(query, filters: CollectionJobFilter):
        """Apply filters to collection job query"""
        conditions = []

        if filters.source_ids:
            conditions.append(DBCollectionJob.source_id.in_(filters.source_ids))

        if filters.statuses:
            conditions.append(DBCollectionJob.status.in_([s.value for s in filters.statuses]))

        if filters.date_from:
            conditions.append(DBCollectionJob.started_at >= filters.date_from)

        if filters.date_to:
            conditions.append(DBCollectionJob.started_at <= filters.date_to)

        if conditions:
            query = query.where(and_(*conditions))

        return query

    @staticmethod
    def _db_to_graphql(db_job: DBCollectionJob) -> CollectionJob:
        """Convert database model to GraphQL type"""
        return CollectionJob(
            id=str(db_job.id),
            source_id=str(db_job.source_id),
            status=db_job.status,
            started_at=db_job.started_at,
            completed_at=db_job.completed_at,
            documents_found=db_job.documents_found,
            documents_new=db_job.documents_new,
            documents_updated=db_job.documents_updated,
            documents_failed=db_job.documents_failed,
            error_message=db_job.error_message,
            retry_count=db_job.retry_count,
            duration_seconds=db_job.duration_seconds,
            bytes_processed=db_job.bytes_processed,
            created_at=db_job.created_at,
            updated_at=db_job.updated_at,
            source=SourceResolver._db_to_graphql(db_job.source) if db_job.source else None
        )


class MutationResolver:
    """Resolvers for GraphQL mutations"""

    @staticmethod
    async def create_action(input_data: CreateActionInput) -> CreateActionResult:
        """Create a new action"""
        try:
            async with get_async_session() as session:
                # Verify regulation exists
                reg_query = select(DBRegulation).where(DBRegulation.id == input_data.regulation_id)
                reg_result = await session.execute(reg_query)
                regulation = reg_result.scalar_one_or_none()

                if not regulation:
                    return CreateActionResult(
                        success=False,
                        message="Regulation not found",
                        errors=["Invalid regulation_id"]
                    )

                # Create new action
                new_action = DBAction(
                    regulation_id=input_data.regulation_id,
                    description=input_data.description,
                    priority=input_data.priority,
                    due_date=input_data.due_date,
                    client_id=input_data.client_id,
                    confidence=1.0,  # Manual actions have full confidence
                    status='pending'
                )

                session.add(new_action)
                await session.commit()
                await session.refresh(new_action)

                # Load relationships
                await session.refresh(new_action, ['regulation'])

                return CreateActionResult(
                    success=True,
                    message="Action created successfully",
                    action=ActionResolver._db_to_graphql(new_action)
                )

        except Exception as e:
            logger.error(f"Failed to create action: {e}")
            return CreateActionResult(
                success=False,
                message="Failed to create action",
                errors=[str(e)]
            )

    @staticmethod
    async def update_action(input_data: UpdateActionInput) -> UpdateActionResult:
        """Update an existing action"""
        try:
            async with get_async_session() as session:
                # Get existing action
                query = select(DBAction).options(selectinload(DBAction.regulation)).where(DBAction.id == input_data.id)
                result = await session.execute(query)
                action = result.scalar_one_or_none()

                if not action:
                    return UpdateActionResult(
                        success=False,
                        message="Action not found",
                        errors=["Invalid action ID"]
                    )

                # Update fields
                if input_data.description is not None:
                    action.description = input_data.description
                if input_data.priority is not None:
                    action.priority = input_data.priority
                if input_data.due_date is not None:
                    action.due_date = input_data.due_date
                if input_data.status is not None:
                    action.status = input_data.status.value

                await session.commit()
                await session.refresh(action)

                return UpdateActionResult(
                    success=True,
                    message="Action updated successfully",
                    action=ActionResolver._db_to_graphql(action)
                )

        except Exception as e:
            logger.error(f"Failed to update action: {e}")
            return UpdateActionResult(
                success=False,
                message="Failed to update action",
                errors=[str(e)]
            )

    @staticmethod
    async def trigger_collection(source_id: strawberry.ID) -> TriggerCollectionResult:
        """Trigger collection for a specific source"""
        try:
            # Use the collector service to trigger collection
            job = await collector_service.collect_from_source(str(source_id))

            if job:
                return TriggerCollectionResult(
                    success=True,
                    message=f"Collection triggered successfully for source {source_id}",
                    job=CollectionJobResolver._db_to_graphql(job)
                )
            else:
                return TriggerCollectionResult(
                    success=False,
                    message="Failed to trigger collection",
                    errors=["Source not found or inactive"]
                )

        except Exception as e:
            logger.error(f"Failed to trigger collection: {e}")
            return TriggerCollectionResult(
                success=False,
                message="Failed to trigger collection",
                errors=[str(e)]
            )


class DashboardResolver:
    """Resolvers for dashboard data and analytics"""

    @staticmethod
    async def get_dashboard_summary() -> DashboardSummary:
        """Get overall dashboard summary"""
        from services.dashboard_service import DashboardService

        dashboard_service = DashboardService()
        summary_data = await dashboard_service.get_dashboard_summary()

        return DashboardSummary(
            total_sources=summary_data.total_sources,
            active_sources=summary_data.active_sources,
            total_documents=summary_data.total_documents,
            documents_24h=summary_data.documents_24h,
            documents_7d=summary_data.documents_7d,
            healthy_sources=summary_data.healthy_sources,
            failing_sources=summary_data.failing_sources,
            overall_success_rate=summary_data.overall_success_rate,
            total_actions=summary_data.total_actions,
            pending_actions=summary_data.pending_actions,
            languages_detected=summary_data.languages_detected,
            recent_collections=summary_data.recent_collections,
            avg_processing_time=summary_data.avg_processing_time
        )

    @staticmethod
    async def get_source_metrics(limit: Optional[int] = None) -> List[SourceMetrics]:
        """Get detailed metrics for each source"""
        from services.dashboard_service import DashboardService

        dashboard_service = DashboardService()
        metrics_data = await dashboard_service.get_source_metrics(limit)

        return [
            SourceMetrics(
                source_id=m.source_id,
                name=m.name,
                agency=m.agency,
                region=m.region,
                is_active=m.is_active,
                total_documents=m.total_documents,
                documents_24h=m.documents_24h,
                documents_7d=m.documents_7d,
                documents_30d=m.documents_30d,
                last_collection_at=m.last_collection_at,
                last_success_at=m.last_success_at,
                consecutive_failures=m.consecutive_failures,
                success_rate_7d=m.success_rate_7d,
                avg_document_length=m.avg_document_length,
                primary_language=m.primary_language,
                document_types=m.document_types,
                intent_distribution=m.intent_distribution
            )
            for m in metrics_data
        ]

    @staticmethod
    async def get_intent_metrics() -> List[IntentMetrics]:
        """Get metrics for each regulatory intent category"""
        from services.dashboard_service import DashboardService

        dashboard_service = DashboardService()
        intent_data = await dashboard_service.get_intent_metrics()

        return [
            IntentMetrics(
                intent=i.intent,
                total_documents=i.total_documents,
                documents_24h=i.documents_24h,
                documents_7d=i.documents_7d,
                documents_30d=i.documents_30d,
                top_sources=i.top_sources,
                regional_distribution=i.regional_distribution,
                daily_counts=i.daily_counts,
                avg_confidence=i.avg_confidence,
                high_confidence_count=i.high_confidence_count
            )
            for i in intent_data
        ]

    @staticmethod
    async def get_trending_topics(days: int = 7) -> List[TrendingTopic]:
        """Get trending regulatory topics"""
        from services.dashboard_service import DashboardService

        dashboard_service = DashboardService()
        trending_data = await dashboard_service.get_trending_topics(days)

        return [
            TrendingTopic(
                topic=t["topic"],
                mentions=t["mentions"],
                trend=t["trend"],
                change_percent=t.get("change_percent")
            )
            for t in trending_data
        ]

    @staticmethod
    async def get_classification_stats(limit: int = 100) -> ClassificationStats:
        """Get intent classification statistics"""
        from services.dashboard_service import DashboardService

        dashboard_service = DashboardService()
        classification_data = await dashboard_service.classify_recent_documents(limit)

        stats = classification_data['statistics']
        samples = classification_data['sample_classifications']

        return ClassificationStats(
            total_classified=classification_data['total_classified'],
            average_confidence=stats.get('average_confidence', 0.0),
            intent_distribution=stats.get('intent_distribution', {}),
            high_confidence_count=stats.get('high_confidence_count', 0),
            low_confidence_count=stats.get('low_confidence_count', 0),
            sample_classifications=[
                IntentClassificationResult(
                    intent=s['intent'],
                    confidence=s['confidence'],
                    reasoning=s['reasoning'],
                    secondary_intents={}  # Could be expanded
                )
                for s in samples
            ]
        )

    @staticmethod
    async def get_complete_dashboard_data() -> DashboardData:
        """Get complete dashboard data in one query"""
        summary = await DashboardResolver.get_dashboard_summary()
        source_metrics = await DashboardResolver.get_source_metrics()
        intent_metrics = await DashboardResolver.get_intent_metrics()
        trending_topics = await DashboardResolver.get_trending_topics()

        try:
            classification_stats = await DashboardResolver.get_classification_stats()
        except Exception as e:
            logger.warning(f"Failed to get classification stats: {e}")
            classification_stats = None

        return DashboardData(
            summary=summary,
            source_metrics=source_metrics,
            intent_metrics=intent_metrics,
            trending_topics=trending_topics,
            classification_stats=classification_stats
        )
